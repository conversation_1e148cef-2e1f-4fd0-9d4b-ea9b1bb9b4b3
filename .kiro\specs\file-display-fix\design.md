# Design Document

## Overview

Дизайн исправления отображения файлов в интерфейсе Gemini AI чата. Основная проблема заключается в том, что изображения и текстовая информация о файлах выходят за границы контейнеров, нарушая визуальную целостность интерфейса.

## Architecture

### Проблемные области
1. **Прикрепленные файлы в панели ввода** - превью изображений не ограничены размерами контейнера
2. **Файлы в сообщениях** - изображения и текст могут выходить за границы сообщения
3. **Адаптивность** - отсутствие корректного поведения на разных размерах экрана
4. **Текстовые переполнения** - длинные имена файлов и размеры не обрезаются

### Архитектурный подход
- **Контейнерная модель**: Все файловые элементы должны быть ограничены размерами родительских контейнеров
- **Responsive design**: Адаптивное поведение для разных размеров экрана
- **Overflow management**: Корректная обработка переполнения контента
- **Consistent sizing**: Единообразные размеры для всех типов файловых превью

## Components and Interfaces

### 1. File Preview Container
**Назначение**: Контейнер для превью прикрепленных файлов
**Ключевые свойства**:
- Фиксированные размеры с overflow: hidden
- Flexbox layout для правильного выравнивания
- Responsive поведение

### 2. Message File Display
**Назначение**: Отображение файлов внутри сообщений
**Ключевые свойства**:
- Ограничение максимальной ширины
- Сохранение пропорций изображений
- Текстовые ellipsis для длинных имен

### 3. File Info Component
**Назначение**: Отображение информации о файле (имя, размер)
**Ключевые свойства**:
- Text overflow с ellipsis
- Responsive font sizes
- Правильное выравнивание

## Data Models

### File Display Configuration
```css
.file-container {
  max-width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.file-image {
  max-width: 48px;
  max-height: 48px;
  object-fit: cover;
  flex-shrink: 0;
}

.file-info {
  flex: 1;
  min-width: 0; /* Важно для text-overflow */
  overflow: hidden;
}

.file-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
```

## Error Handling

### CSS Fallbacks
1. **Изображения без размеров**: Установка default размеров через CSS
2. **Длинные тексты**: Автоматическое обрезание с ellipsis
3. **Переполнение контейнеров**: overflow: hidden на всех уровнях
4. **Отсутствие flex поддержки**: Fallback на block layout

### Responsive Breakpoints
- **Desktop**: > 768px - полные размеры
- **Tablet**: 481-768px - уменьшенные превью
- **Mobile**: ≤ 480px - минимальные размеры

## Testing Strategy

### Visual Regression Tests
1. **Различные размеры изображений**: от маленьких до очень больших
2. **Длинные имена файлов**: тестирование ellipsis
3. **Множественные файлы**: проверка layout при нескольких файлах
4. **Responsive поведение**: тестирование на разных размерах экрана

### CSS Validation
1. **Cross-browser compatibility**: Chrome, Firefox, Safari, Edge
2. **Mobile devices**: iOS Safari, Chrome Mobile
3. **High DPI displays**: проверка на Retina дисплеях

### Performance Considerations
1. **CSS оптимизация**: минимизация reflow/repaint
2. **Image loading**: lazy loading для больших изображений
3. **Memory usage**: ограничение размеров превью для экономии памяти

## Implementation Details

### CSS Fixes Required

#### 1. Attached Files Container
```css
.attached-files-container {
  max-width: 100%;
  overflow: hidden;
}

.file-preview {
  max-width: 100%;
  overflow: hidden;
}

.file-preview-image {
  width: 48px;
  height: 48px;
  object-fit: cover;
  flex-shrink: 0;
}
```

#### 2. Message Files
```css
.message-file {
  max-width: 100%;
  overflow: hidden;
}

.message-file-image {
  max-width: 120px;
  max-height: 120px;
  object-fit: cover;
}

.message-file-info {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}
```

#### 3. Text Overflow
```css
.file-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.file-size {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
```

#### 4. Responsive Adjustments
```css
@media (max-width: 768px) {
  .file-preview-image,
  .message-file-image {
    width: 40px;
    height: 40px;
  }
  
  .file-name {
    font-size: 0.8rem;
  }
  
  .file-size {
    font-size: 0.7rem;
  }
}
```

### JavaScript Enhancements
- Добавление title атрибутов для полных имен файлов
- Динамическое изменение размеров превью на основе размера контейнера
- Lazy loading для изображений в сообщениях