<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini AI Chat - Современный интерфейс</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>

<body>
    <div class="app-container">
        <!-- Заголовок приложения -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h1 class="app-title">Gemini AI</h1>
                </div>
                <div class="header-controls">
                    <button class="theme-toggle" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="notifications-demo-btn" id="notificationsDemoBtn" title="Демо уведомлений">
                        <i class="fas fa-bell"></i>
                    </button>
                    <button class="settings-btn" id="settingsBtn">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Основной контент -->
        <main class="main-content">
            <!-- Боковая панель -->
            <aside class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <h3>История чатов</h3>
                    <div class="chat-controls">
                        <button class="new-chat-btn" id="newChatBtn">
                            <i class="fas fa-plus"></i>
                            Новый чат
                        </button>
                        <div class="chat-management-buttons">
                            <button class="chat-control-btn" id="exportAllBtn" title="Экспорт всех чатов">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="chat-control-btn" id="clearAllBtn" title="Очистить все чаты">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                            <button class="chat-control-btn" id="searchChatsBtn" title="Поиск в чатах">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="chat-history" id="chatHistory">
                    <!-- История чатов будет добавлена динамически -->
                </div>
            </aside>

            <!-- Область чата -->
            <section class="chat-area">
                <div class="chat-container" id="chatContainer">
                    <div class="welcome-screen" id="welcomeScreen">
                        <div class="welcome-content">
                            <div class="welcome-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h2>Добро пожаловать в Gemini AI</h2>
                            <p>Начните разговор с искусственным интеллектом</p>
                            <div class="suggestion-cards">
                                <div class="suggestion-card" data-prompt="Расскажи мне интересный факт">
                                    <i class="fas fa-lightbulb"></i>
                                    <span>Интересный факт</span>
                                </div>
                                <div class="suggestion-card" data-prompt="Помоги мне написать код">
                                    <i class="fas fa-code"></i>
                                    <span>Помощь с кодом</span>
                                </div>
                                <div class="suggestion-card" data-prompt="Объясни сложную тему простыми словами">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>Объяснение</span>
                                </div>
                                <div class="suggestion-card" data-prompt="Придумай креативную идею">
                                    <i class="fas fa-palette"></i>
                                    <span>Креативность</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="messages-container" id="messagesContainer">
                        <!-- Сообщения будут добавлены динамически -->
                    </div>
                </div>

                <!-- Панель ввода -->
                <div class="input-panel">
                    <div class="input-container">
                        <div class="input-wrapper">
                            <textarea id="messageInput" placeholder="Введите ваше сообщение..." rows="1"
                                maxlength="4000"></textarea>
                            <div class="input-actions">
                                <button class="attach-btn" id="attachBtn" title="Прикрепить изображение">
                                    <i class="fas fa-image"></i>
                                </button>
                                <button class="send-btn" id="sendBtn" title="Отправить">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                        <div class="input-footer">
                            <span class="char-counter" id="charCounter">0/4000</span>
                            <span class="typing-speed-indicator" id="typingSpeedIndicator">
                                <i class="fas fa-keyboard"></i>
                                <span id="speedValue">0</span> сим/сек
                            </span>
                            <span class="ai-status" id="aiStatus">
                                <i class="fas fa-circle"></i>
                                Готов к работе
                            </span>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Модальное окно настроек -->
        <div class="modal-overlay" id="settingsModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Настройки</h3>
                    <button class="close-btn" id="closeSettingsBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <label for="apiKey">API Ключ Gemini:</label>
                        <input type="password" id="apiKey" placeholder="Введите ваш API ключ">
                        <small>Ваш API ключ сохраняется локально в браузере</small>
                        <div class="api-help">
                            <details>
                                <summary>Как получить API ключ?</summary>
                                <div class="help-content">
                                    <ol>
                                        <li>Перейдите на <a href="https://makersuite.google.com/app/apikey"
                                                target="_blank">Google AI Studio</a></li>
                                        <li>Войдите в свой Google аккаунт</li>
                                        <li>Нажмите "Create API Key"</li>
                                        <li>Выберите проект или создайте новый</li>
                                        <li>Скопируйте полученный ключ и вставьте сюда</li>
                                    </ol>
                                    <p><strong>Важно:</strong> API ключ предоставляет доступ к вашему аккаунту Google
                                        AI. Не делитесь им с другими!</p>
                                </div>
                            </details>
                        </div>
                    </div>
                    <div class="setting-group">
                        <label for="model">Модель Gemini:</label>
                        <select id="model">
                            <option value="gemini-2.5-pro">Gemini 2.5 Pro (продвинутая)</option>
                            <option value="gemini-2.5-flash">Gemini 2.5 Flash (быстрая)</option>
                            <option value="gemini-2.5-flash-lite-preview-06-17">Gemini 2.5 Flash Lite (облегченная)
                            </option>
                        </select>
                        <small>2.5 Pro - самая продвинутая модель, 2.5 Flash - быстрая и эффективная, 2.5 Flash Lite -
                            облегченная версия</small>
                    </div>
                    <div class="setting-group">
                        <label for="temperature">Температура (креативность):</label>
                        <div class="temperature-container">
                            <input type="range" id="temperature" min="0" max="1" step="0.1" value="0.7">
                            <span class="temperature-value" id="temperatureValue">0.7</span>
                        </div>
                        <div class="slider-labels">
                            <span>Точный (0.0)</span>
                            <span>Сбалансированный (0.5)</span>
                            <span>Креативный (1.0)</span>
                        </div>
                        <small>Низкие значения дают более точные ответы, высокие - более креативные</small>
                    </div>
                    <div class="setting-group">
                        <label for="maxTokens">Максимум токенов:</label>
                        <input type="number" id="maxTokens" min="100" max="4000" value="1000">
                        <small>Максимальное количество токенов в ответе (100-4000)</small>
                    </div>
                    <div class="setting-group">
                        <label>Настройки интерфейса:</label>
                        <div class="interface-settings">
                            <div class="setting-item">
                                <div class="setting-header">
                                    <div class="setting-icon">
                                        <i class="fas fa-magic"></i>
                                    </div>
                                    <label class="toggle-label">
                                        <span class="setting-title">Анимации интерфейса</span>
                                        <input type="checkbox" id="enableAnimations" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <small class="setting-description">Включить плавные анимации и переходы</small>
                            </div>
                            <div class="setting-item">
                                <div class="setting-header">
                                    <div class="setting-icon">
                                        <i class="fas fa-volume-up"></i>
                                    </div>
                                    <label class="toggle-label">
                                        <span class="setting-title">Звуковые уведомления</span>
                                        <input type="checkbox" id="enableSounds" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <small class="setting-description">
                                    Воспроизводить звуки при получении сообщений
                                    <button type="button" class="test-sound-btn" id="testSoundBtn">
                                        <i class="fas fa-play"></i> Тест
                                    </button>
                                </small>
                            </div>
                            <div class="setting-item">
                                <div class="setting-header">
                                    <div class="setting-icon">
                                        <i class="fas fa-keyboard"></i>
                                    </div>
                                    <label class="toggle-label">
                                        <span class="setting-title">Эффект печатания</span>
                                        <input type="checkbox" id="enableTypingEffect" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <small class="setting-description">Показывать текст AI с эффектом печатания</small>
                            </div>
                            <div class="setting-item">
                                <div class="setting-header">
                                    <div class="setting-icon">
                                        <i class="fas fa-save"></i>
                                    </div>
                                    <label class="toggle-label">
                                        <span class="setting-title">Автосохранение</span>
                                        <input type="checkbox" id="autoSave" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <small class="setting-description">Автоматически сохранять чаты каждые 30 секунд</small>
                            </div>
                        </div>
                    </div>
                    <div class="setting-group">
                        <label for="fontSize">Размер шрифта:</label>
                        <div class="font-size-container">
                            <input type="range" id="fontSize" min="12" max="20" step="1" value="16">
                            <span class="font-size-value" id="fontSizeValue">16px</span>
                        </div>
                        <div class="font-size-labels">
                            <span>Мелкий (12px)</span>
                            <span>Средний (16px)</span>
                            <span>Крупный (20px)</span>
                        </div>
                        <small>Размер шрифта в сообщениях</small>
                    </div>
                    <div class="setting-group">
                        <label for="messageSpeed">Скорость печатания AI:</label>
                        <div class="speed-container">
                            <input type="range" id="messageSpeed" min="5" max="50" step="5" value="15">
                            <span class="speed-value" id="speedValueDisplay">15ms</span>
                        </div>
                        <div class="speed-labels">
                            <span>Быстро (5ms)</span>
                            <span>Средне (25ms)</span>
                            <span>Медленно (50ms)</span>
                        </div>
                        <small>Скорость появления символов при печатании</small>
                    </div>
                    <div class="setting-group">
                        <label>Резервное копирование:</label>
                        <div class="backup-controls">
                            <button type="button" class="btn-secondary" id="exportSettingsBtn">
                                <i class="fas fa-download"></i>
                                Экспорт настроек
                            </button>
                            <button type="button" class="btn-secondary" id="importSettingsBtn">
                                <i class="fas fa-upload"></i>
                                Импорт настроек
                            </button>
                        </div>
                        <small>Экспорт включает настройки и историю чатов</small>
                    </div>
                    <div class="setting-group">
                        <label>Статистика использования:</label>
                        <div class="stats-display" id="statsDisplay">
                            <div class="stat-item">
                                <span class="stat-label">Всего чатов:</span>
                                <span class="stat-value" id="totalChats">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Всего сообщений:</span>
                                <span class="stat-value" id="totalMessages">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Последняя активность:</span>
                                <span class="stat-value" id="lastActivity">Никогда</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" id="cancelSettingsBtn">Отмена</button>
                    <button class="btn-primary" id="saveSettingsBtn">Сохранить</button>
                </div>
            </div>
        </div>

        <!-- Модальное окно поиска чатов -->
        <div class="modal-overlay" id="searchModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Поиск в чатах</h3>
                    <button class="close-btn" id="closeSearchBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <label for="searchInput">Поиск по содержимому:</label>
                        <input type="text" id="searchInput" placeholder="Введите текст для поиска...">
                        <small>Поиск выполняется по всем сообщениям в истории чатов</small>
                    </div>
                    <div class="search-results" id="searchResults">
                        <!-- Результаты поиска будут добавлены динамически -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" id="cancelSearchBtn">Закрыть</button>
                </div>
            </div>
        </div>

        <!-- Модальное окно подтверждения удаления -->
        <div class="modal-overlay" id="confirmModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Подтверждение действия</h3>
                    <button class="close-btn" id="closeConfirmBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="confirm-content">
                        <div class="confirm-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <p id="confirmMessage">Вы уверены, что хотите выполнить это действие?</p>
                        <small id="confirmDetails">Это действие нельзя будет отменить.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" id="cancelConfirmBtn">Отмена</button>
                    <button class="btn-danger" id="confirmActionBtn">Подтвердить</button>
                </div>
            </div>
        </div>

        <!-- Индикатор загрузки -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>Gemini думает...</p>
            </div>
        </div>

        <!-- Кастомный курсор -->
        <div class="custom-cursor" id="customCursor"></div>
    </div>

    <script src="script.js"></script>
</body>

</html>