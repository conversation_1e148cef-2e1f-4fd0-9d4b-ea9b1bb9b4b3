// Глобальные переменные
let currentChatId = null;
let currentChatMessages = [];
let chatHistory = [];
let isTyping = false;
let settings = {
    apiKey: '',
    model: 'gemini-2.5-flash',
    temperature: 0.7,
    maxTokens: 1000,
    enableAnimations: true,
    enableSounds: true,
    enableTypingEffect: true,
    autoSave: true,
    fontSize: 16,
    messageSpeed: 15
};

// Переменные для кастомного курсора
let customCursor = null;
let cursorTimeout = null;

// Переменные для измерения скорости печати
let typingStartTime = null;
let lastTypingTime = null;
let typingHistory = [];
let typingSpeedTimer = null;

// DOM элементы
const elements = {
    themeToggle: document.getElementById('themeToggle'),
    notificationsDemoBtn: document.getElementById('notificationsDemoBtn'),
    settingsBtn: document.getElementById('settingsBtn'),
    settingsModal: document.getElementById('settingsModal'),
    closeSettingsBtn: document.getElementById('closeSettingsBtn'),
    saveSettingsBtn: document.getElementById('saveSettingsBtn'),
    cancelSettingsBtn: document.getElementById('cancelSettingsBtn'),
    newChatBtn: document.getElementById('newChatBtn'),
    messageInput: document.getElementById('messageInput'),
    sendBtn: document.getElementById('sendBtn'),
    attachBtn: document.getElementById('attachBtn'),
    charCounter: document.getElementById('charCounter'),
    typingSpeedIndicator: document.getElementById('typingSpeedIndicator'),
    speedValue: document.getElementById('speedValue'),
    aiStatus: document.getElementById('aiStatus'),
    welcomeScreen: document.getElementById('welcomeScreen'),
    messagesContainer: document.getElementById('messagesContainer'),
    chatHistory: document.getElementById('chatHistory'),
    loadingOverlay: document.getElementById('loadingOverlay'),
    apiKey: document.getElementById('apiKey'),
    model: document.getElementById('model'),
    temperature: document.getElementById('temperature'),
    temperatureValue: document.getElementById('temperatureValue'),
    maxTokens: document.getElementById('maxTokens'),
    enableAnimations: document.getElementById('enableAnimations'),
    enableSounds: document.getElementById('enableSounds'),
    enableTypingEffect: document.getElementById('enableTypingEffect'),
    autoSave: document.getElementById('autoSave'),
    fontSize: document.getElementById('fontSize'),
    fontSizeValue: document.getElementById('fontSizeValue'),
    messageSpeed: document.getElementById('messageSpeed'),
    speedValueDisplay: document.getElementById('speedValueDisplay'),
    exportAllBtn: document.getElementById('exportAllBtn'),
    clearAllBtn: document.getElementById('clearAllBtn'),
    searchChatsBtn: document.getElementById('searchChatsBtn'),
    exportSettingsBtn: document.getElementById('exportSettingsBtn'),
    importSettingsBtn: document.getElementById('importSettingsBtn'),
    searchModal: document.getElementById('searchModal'),
    closeSearchBtn: document.getElementById('closeSearchBtn'),
    cancelSearchBtn: document.getElementById('cancelSearchBtn'),
    searchInput: document.getElementById('searchInput'),
    searchResults: document.getElementById('searchResults'),
    confirmModal: document.getElementById('confirmModal'),
    closeConfirmBtn: document.getElementById('closeConfirmBtn'),
    cancelConfirmBtn: document.getElementById('cancelConfirmBtn'),
    confirmActionBtn: document.getElementById('confirmActionBtn'),
    confirmMessage: document.getElementById('confirmMessage'),
    confirmDetails: document.getElementById('confirmDetails'),
    customCursor: document.getElementById('customCursor')
};

// Функции для кастомного курсора
function initCustomCursor() {
    customCursor = elements.customCursor;

    if (!customCursor) return;

    // Отслеживание движения мыши
    document.addEventListener('mousemove', updateCursorPosition);

    // Отслеживание наведения на интерактивные элементы
    const buttonElements = 'button, [role="button"], .btn, .send-btn, .attach-btn, .new-chat-btn';
    const linkElements = 'a, .suggestion-card, .chat-history-item';
    const inputElements = 'input, textarea, select';

    document.addEventListener('mouseover', (e) => {
        const target = e.target;

        // Проверяем тип элемента и устанавливаем соответствующее состояние
        if (target.matches(buttonElements) || target.closest(buttonElements)) {
            setCursorState('button');
        } else if (target.matches(linkElements) || target.closest(linkElements)) {
            setCursorState('link');
        } else if (target.matches(inputElements) || target.closest(inputElements)) {
            setCursorState('text');
        } else if (target.matches('.message') || target.closest('.message')) {
            setCursorState('hover');
        }
    });

    document.addEventListener('mouseout', (e) => {
        const target = e.target;

        if (target.matches(buttonElements + ', ' + linkElements + ', ' + inputElements + ', .message') ||
            target.closest(buttonElements + ', ' + linkElements + ', ' + inputElements + ', .message')) {
            setCursorState('default');
        }
    });

    // Отслеживание кликов
    document.addEventListener('mousedown', () => {
        setCursorState('click');
    });

    document.addEventListener('mouseup', () => {
        setCursorState('default');
    });

    // Отслеживание текстовых полей
    const textElements = 'input[type="text"], input[type="password"], input[type="email"], textarea';

    document.addEventListener('mouseover', (e) => {
        if (e.target.matches(textElements)) {
            setCursorState('text');
        }
    });

    document.addEventListener('mouseout', (e) => {
        if (e.target.matches(textElements)) {
            setCursorState('default');
        }
    });

    // Скрытие курсора при выходе из окна
    document.addEventListener('mouseleave', () => {
        setCursorState('hidden');
    });

    document.addEventListener('mouseenter', () => {
        setCursorState('default');
    });

    // Специальные состояния для загрузки
    const loadingObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.target.id === 'loadingOverlay') {
                const isVisible = mutation.target.style.display !== 'none' &&
                                mutation.target.classList.contains('active');
                if (isVisible) {
                    setCursorState('loading');
                } else {
                    setCursorState('default');
                }
            }
        });
    });

    if (elements.loadingOverlay) {
        loadingObserver.observe(elements.loadingOverlay, {
            attributes: true,
            attributeFilter: ['style', 'class']
        });
    }
}

function updateCursorPosition(e) {
    if (!customCursor) return;

    requestAnimationFrame(() => {
        customCursor.style.left = e.clientX + 'px';
        customCursor.style.top = e.clientY + 'px';
    });
}

function setCursorState(state) {
    if (!customCursor) return;

    // Очищаем все классы состояний
    customCursor.classList.remove('hover', 'click', 'text', 'loading', 'hidden', 'pulse', 'button', 'link', 'drag');

    // Добавляем новое состояние
    if (state !== 'default') {
        customCursor.classList.add(state);
    }

    // Автоматическое возвращение к default после клика
    if (state === 'click') {
        clearTimeout(cursorTimeout);
        cursorTimeout = setTimeout(() => {
            setCursorState('default');
        }, 150);
    }
}

function setCursorPulse(enable = true) {
    if (!customCursor) return;

    if (enable) {
        customCursor.classList.add('pulse');
    } else {
        customCursor.classList.remove('pulse');
    }
}

// Инициализация приложения
document.addEventListener('DOMContentLoaded', function () {
    initializeApp();
    setupEventListeners();
    loadSettings();
    updateCharCounter();
    initCustomCursor(); // Инициализируем кастомный курсор
});

function initializeApp() {
    // Проверка темы
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    updateThemeIcon(savedTheme);

    // Загрузка истории чатов
    loadChatHistory();

    // Автофокус на поле ввода
    setTimeout(() => {
        if (elements.messageInput) {
            elements.messageInput.focus();
        }
    }, 100);
}

function setupEventListeners() {
    // Переключение темы
    elements.themeToggle.addEventListener('click', toggleTheme);

    // Демо уведомлений
    elements.notificationsDemoBtn.addEventListener('click', showNotificationsDemo);

    // Модальное окно настроек
    elements.settingsBtn.addEventListener('click', openSettings);
    elements.closeSettingsBtn.addEventListener('click', closeSettings);
    elements.cancelSettingsBtn.addEventListener('click', closeSettings);
    elements.saveSettingsBtn.addEventListener('click', saveSettings);

    // Закрытие модального окна по клику вне его
    elements.settingsModal.addEventListener('click', function (e) {
        if (e.target === elements.settingsModal) {
            closeSettings();
        }
    });

    // Новый чат
    elements.newChatBtn.addEventListener('click', startNewChat);

    // Отправка сообщения
    elements.sendBtn.addEventListener('click', sendMessage);
    elements.messageInput.addEventListener('keydown', function (e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Автоизменение размера textarea
    elements.messageInput.addEventListener('input', function () {
        autoResizeTextarea(this);
        updateCharCounter();
        updateSendButton();
        handleTypingSpeedTracking();
    });

    // Отслеживание начала печати
    elements.messageInput.addEventListener('keydown', function (e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
            return;
        }

        // Начинаем отслеживание скорости печати
        if (!typingStartTime) {
            typingStartTime = Date.now();
        }
        lastTypingTime = Date.now();
    });

    // Сброс скорости при потере фокуса
    elements.messageInput.addEventListener('blur', function () {
        resetTypingSpeed();
    });

    // Слайдер температуры
    elements.temperature.addEventListener('input', function () {
        elements.temperatureValue.textContent = this.value;
    });

    // Слайдер размера шрифта
    elements.fontSize.addEventListener('input', function () {
        elements.fontSizeValue.textContent = this.value + 'px';
        applyFontSize(this.value);
    });

    // Слайдер скорости печатания
    elements.messageSpeed.addEventListener('input', function () {
        elements.speedValueDisplay.textContent = this.value + 'ms';
    });

    // Переключатели настроек
    elements.enableAnimations.addEventListener('change', function () {
        toggleAnimations(this.checked);
    });

    elements.enableSounds.addEventListener('change', function () {
        settings.enableSounds = this.checked;
    });

    elements.enableTypingEffect.addEventListener('change', function () {
        settings.enableTypingEffect = this.checked;
    });

    elements.autoSave.addEventListener('change', function () {
        settings.autoSave = this.checked;
        if (this.checked) {
            startAutoSave();
        } else {
            stopAutoSave();
        }
    });

    // Кнопки экспорта и импорта
    elements.exportSettingsBtn.addEventListener('click', exportSettings);
    elements.importSettingsBtn.addEventListener('click', importSettings);

    // Кнопка тестирования звука
    const testSoundBtn = document.getElementById('testSoundBtn');
    if (testSoundBtn) {
        testSoundBtn.addEventListener('click', function() {
            playNotificationSound();
            showNotification('Тестовый звук воспроизведен', 'success', {
                duration: 2000,
                icon: 'fas fa-volume-up'
            });
        });
    }

    // Карточки предложений
    document.querySelectorAll('.suggestion-card').forEach(card => {
        card.addEventListener('click', function () {
            const prompt = this.getAttribute('data-prompt');
            elements.messageInput.value = prompt;
            autoResizeTextarea(elements.messageInput);
            updateCharCounter();
            updateSendButton();
            elements.messageInput.focus();
        });
    });

    // Прикрепление файлов
    elements.attachBtn.addEventListener('click', function () {
        openFileDialog();
    });

    // Drag and Drop для изображений
    setupDragAndDrop();

    // Управление чатами
    elements.exportAllBtn.addEventListener('click', exportAllChats);
    elements.clearAllBtn.addEventListener('click', confirmClearAllChats);
    elements.searchChatsBtn.addEventListener('click', openSearchModal);

    // Модальное окно поиска
    elements.closeSearchBtn.addEventListener('click', closeSearchModal);
    elements.cancelSearchBtn.addEventListener('click', closeSearchModal);
    elements.searchInput.addEventListener('input', performSearch);

    // Закрытие модального окна поиска по клику вне его
    elements.searchModal.addEventListener('click', function (e) {
        if (e.target === elements.searchModal) {
            closeSearchModal();
        }
    });

    // Модальное окно подтверждения
    elements.closeConfirmBtn.addEventListener('click', closeConfirmModal);
    elements.cancelConfirmBtn.addEventListener('click', closeConfirmModal);

    // Закрытие модального окна подтверждения по клику вне его
    elements.confirmModal.addEventListener('click', function (e) {
        if (e.target === elements.confirmModal) {
            closeConfirmModal();
        }
    });
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateThemeIcon(newTheme);
}

// Убираем сложные эффекты переключения темы

function updateThemeIcon(theme) {
    const icon = elements.themeToggle.querySelector('i');
    icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
}

function openSettings() {
    elements.settingsModal.classList.add('active');

    // Заполняем поля текущими значениями
    elements.apiKey.value = settings.apiKey;
    elements.model.value = settings.model;
    elements.temperature.value = settings.temperature;
    elements.temperatureValue.textContent = settings.temperature;
    elements.maxTokens.value = settings.maxTokens;
    elements.enableAnimations.checked = settings.enableAnimations;
    elements.enableSounds.checked = settings.enableSounds;
    elements.enableTypingEffect.checked = settings.enableTypingEffect;
    elements.autoSave.checked = settings.autoSave;
    elements.fontSize.value = settings.fontSize;
    elements.fontSizeValue.textContent = settings.fontSize + 'px';
    elements.messageSpeed.value = settings.messageSpeed;
    elements.speedValueDisplay.textContent = settings.messageSpeed + 'ms';

    // Обновляем статистику
    updateUsageStats();

    // Фокус на первое поле
    setTimeout(() => {
        if (!settings.apiKey) {
            elements.apiKey.focus();
        }
    }, 200);
}

function closeSettings() {
    elements.settingsModal.classList.remove('active');
}

async function saveSettings() {
    const newApiKey = elements.apiKey.value.trim();
    const newModel = elements.model.value;
    const newTemperature = parseFloat(elements.temperature.value);
    const newMaxTokens = parseInt(elements.maxTokens.value);
    const newEnableAnimations = elements.enableAnimations.checked;
    const newEnableSounds = elements.enableSounds.checked;
    const newEnableTypingEffect = elements.enableTypingEffect.checked;
    const newAutoSave = elements.autoSave.checked;
    const newFontSize = parseInt(elements.fontSize.value);
    const newMessageSpeed = parseInt(elements.messageSpeed.value);

    // Проверка API ключа, если он изменился
    if (newApiKey && newApiKey !== settings.apiKey) {
        elements.loadingOverlay.classList.add('active');

        try {
            await testApiKey(newApiKey, newModel);
            showNotification('API ключ успешно проверен!', 'success');
        } catch (error) {
            elements.loadingOverlay.classList.remove('active');
            showNotification(`Ошибка API ключа: ${error.message}`, 'error');
            return;
        }

        elements.loadingOverlay.classList.remove('active');
    }

    settings.apiKey = newApiKey;
    settings.model = newModel;
    settings.temperature = newTemperature;
    settings.maxTokens = newMaxTokens;
    settings.enableAnimations = newEnableAnimations;
    settings.enableSounds = newEnableSounds;
    settings.enableTypingEffect = newEnableTypingEffect;
    settings.autoSave = newAutoSave;
    settings.fontSize = newFontSize;
    settings.messageSpeed = newMessageSpeed;

    localStorage.setItem('gemini-settings', JSON.stringify(settings));

    // Применяем настройки
    applySettings();

    closeSettings();
    updateAIStatus();
    showNotification('Настройки сохранены', 'success', {
        icon: 'fas fa-check-circle',
        duration: 3000
    });
}

async function testApiKey(apiKey, model = 'gemini-2.5-flash') {
    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;

    const testRequest = {
        contents: [{
            parts: [{
                text: "Привет! Это тестовое сообщение для проверки API ключа."
            }]
        }],
        generationConfig: {
            maxOutputTokens: 10
        }
    };

    const response = await fetch(`${API_URL}?key=${apiKey}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(testRequest)
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        if (response.status === 401) {
            throw new Error('Неверный API ключ');
        } else if (response.status === 403) {
            throw new Error('API ключ не имеет необходимых разрешений');
        } else {
            throw new Error(errorData.error?.message || `HTTP ${response.status}`);
        }
    }

    const data = await response.json();

    if (!data.candidates || data.candidates.length === 0) {
        throw new Error('API ключ не может генерировать ответы');
    }

    return true;
}

function loadSettings() {
    const savedSettings = localStorage.getItem('gemini-settings');
    if (savedSettings) {
        settings = { ...settings, ...JSON.parse(savedSettings) };
    }
    applySettings();
    updateAIStatus();
}

function applySettings() {
    // Применяем размер шрифта
    applyFontSize(settings.fontSize);
    
    // Применяем настройки анимаций
    toggleAnimations(settings.enableAnimations);
    
    // Запускаем автосохранение если включено
    if (settings.autoSave) {
        startAutoSave();
    }
}

function applyFontSize(size) {
    document.documentElement.style.setProperty('--message-font-size', size + 'px');
}

function toggleAnimations(enabled) {
    if (enabled) {
        document.body.classList.remove('no-animations');
    } else {
        document.body.classList.add('no-animations');
    }
    settings.enableAnimations = enabled;
}

// Автосохранение
let autoSaveInterval = null;

function startAutoSave() {
    if (autoSaveInterval) {
        clearInterval(autoSaveInterval);
    }
    
    autoSaveInterval = setInterval(() => {
        if (currentChatMessages.length > 0) {
            saveCurrentChat();
            showNotification('Чат автоматически сохранен', 'info', {
                duration: 2000,
                icon: 'fas fa-save'
            });
        }
    }, 30000); // Каждые 30 секунд
}

function stopAutoSave() {
    if (autoSaveInterval) {
        clearInterval(autoSaveInterval);
        autoSaveInterval = null;
    }
}

// Обновление статистики использования
function updateUsageStats() {
    const totalChats = chatHistory.length;
    const totalMessages = chatHistory.reduce((sum, chat) => sum + (chat.messages ? chat.messages.length : 0), 0);
    const lastActivity = chatHistory.length > 0 ? 
        new Date(Math.max(...chatHistory.map(chat => new Date(chat.date)))).toLocaleString() : 
        'Никогда';

    if (elements.statsDisplay) {
        const totalChatsEl = document.getElementById('totalChats');
        const totalMessagesEl = document.getElementById('totalMessages');
        const lastActivityEl = document.getElementById('lastActivity');

        if (totalChatsEl) totalChatsEl.textContent = totalChats;
        if (totalMessagesEl) totalMessagesEl.textContent = totalMessages;
        if (lastActivityEl) lastActivityEl.textContent = lastActivity;
    }
}

// Звуковые уведомления
function playNotificationSound() {
    if (!settings.enableSounds) return;
    
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
    } catch (error) {
        // Звук не критичен, игнорируем ошибки
        console.warn('Не удалось воспроизвести звук уведомления:', error);
    }
}

// Функции экспорта и импорта настроек
function exportSettings() {
    const dataToExport = {
        settings: settings,
        chatHistory: chatHistory,
        exportDate: new Date().toISOString(),
        version: '2.0'
    };
    
    const dataStr = JSON.stringify(dataToExport, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `gemini-ai-backup-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    showNotification('Настройки и данные экспортированы', 'success', {
        icon: 'fas fa-download',
        duration: 3000
    });
}

function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importedData = JSON.parse(e.target.result);
                
                if (importedData.settings) {
                    settings = { ...settings, ...importedData.settings };
                    localStorage.setItem('gemini-settings', JSON.stringify(settings));
                    applySettings();
                }
                
                if (importedData.chatHistory) {
                    chatHistory = importedData.chatHistory;
                    localStorage.setItem('gemini-chat-history', JSON.stringify(chatHistory));
                    renderChatHistory();
                }
                
                showNotification('Настройки и данные импортированы', 'success', {
                    icon: 'fas fa-upload',
                    duration: 3000,
                    actions: [{
                        id: 'reload',
                        text: 'Перезагрузить',
                        primary: true,
                        handler: () => window.location.reload()
                    }]
                });
                
                // Обновляем интерфейс настроек если он открыт
                if (elements.settingsModal.classList.contains('active')) {
                    openSettings();
                }
                
            } catch (error) {
                showNotification('Ошибка при импорте файла', 'error', {
                    icon: 'fas fa-exclamation-triangle'
                });
            }
        };
        reader.readAsText(file);
    };
    
    input.click();
}

function updateAIStatus() {
    if (!settings.apiKey) {
        elements.aiStatus.innerHTML = `
            <i class="fas fa-circle status-indicator warning" style="color: var(--warning-color)"></i>
            Требуется API ключ
        `;
        return;
    }

    if (isTyping) {
        elements.aiStatus.innerHTML = `
            <i class="fas fa-brain status-indicator thinking" style="color: var(--primary-color)"></i>
            Думаю...
        `;
        return;
    }

    elements.aiStatus.innerHTML = `
        <i class="fas fa-circle status-indicator ready" style="color: var(--success-color)"></i>
        Готов к работе
    `;
}

function autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
}

function updateCharCounter() {
    const length = elements.messageInput.value.length;
    const maxLength = 4000;

    elements.charCounter.textContent = `${length}/${maxLength}`;

    // Убираем предыдущие классы
    elements.charCounter.classList.remove('warning', 'error');

    if (length > maxLength * 0.9) {
        elements.charCounter.classList.add('error');
    } else if (length > maxLength * 0.8) {
        elements.charCounter.classList.add('warning');
    }
}

function updateSendButton() {
    const hasText = elements.messageInput.value.trim().length > 0;
    const hasApiKey = settings.apiKey.length > 0;

    elements.sendBtn.disabled = !hasText || !hasApiKey || isTyping;
}

async function sendMessage() {
    const message = elements.messageInput.value.trim();
    if (!message || !settings.apiKey || isTyping) return;

    // Добавляем сообщение пользователя
    addMessage(message, 'user');

    // Очищаем поле ввода
    elements.messageInput.value = '';
    autoResizeTextarea(elements.messageInput);
    updateCharCounter();
    updateSendButton();

    // Скрываем экран приветствия
    elements.welcomeScreen.style.display = 'none';
    elements.messagesContainer.classList.add('active');

    // Показываем индикатор печати
    isTyping = true;
    updateAIStatus();
    updateSendButton();
    setCursorPulse(true); // Включаем пульсацию курсора
    const typingIndicator = addTypingIndicator();

    try {
        const response = await callGeminiAPI(message);
        removeTypingIndicator(typingIndicator);
        addMessage(response, 'ai');
    } catch (error) {
        removeTypingIndicator(typingIndicator);
        addMessage(`Ошибка: ${error.message}`, 'error');
    }

    isTyping = false;
    setCursorPulse(false); // Выключаем пульсацию курсора
    updateAIStatus();
    updateSendButton();
}

async function callGeminiAPI(message) {
    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${settings.model}:generateContent`;

    const requestBody = {
        contents: [{
            parts: [{
                text: message
            }]
        }],
        generationConfig: {
            temperature: settings.temperature,
            maxOutputTokens: settings.maxTokens
        }
    };

    const response = await fetch(`${API_URL}?key=${settings.apiKey}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error?.message || `HTTP ${response.status}`);
    }

    const data = await response.json();

    if (!data.candidates || data.candidates.length === 0) {
        throw new Error('Нет ответа от AI');
    }

    return data.candidates[0].content.parts[0].text;
}

function addMessage(content, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    messageDiv.appendChild(messageContent);
    
    // Добавляем с начальной прозрачностью для плавного появления
    messageDiv.style.opacity = '0';
    messageDiv.style.transform = 'translateY(15px)';
    elements.messagesContainer.appendChild(messageDiv);

    // Плавное появление сообщения
    requestAnimationFrame(() => {
        messageDiv.style.transition = 'all 0.4s cubic-bezier(0.25, 1, 0.5, 1)';
        messageDiv.style.opacity = '1';
        messageDiv.style.transform = 'translateY(0)';
    });

    // Для AI сообщений добавляем эффект печатания с задержкой и markdown
    if (type === 'ai') {
        // Воспроизводим звук получения сообщения
        if (settings.enableSounds) {
            playNotificationSound();
        }
        
        setTimeout(() => {
            typeWriterEffectWithMarkdown(messageContent, content);
        }, 200); // Небольшая задержка для плавности
    } else {
        messageContent.textContent = content;
    }

    // Добавляем сообщение в текущий чат
    const messageData = {
        content: content,
        type: type,
        timestamp: new Date().toISOString()
    };

    currentChatMessages.push(messageData);

    // Сохраняем чат
    saveChatMessage(messageData);

    // Прокрутка к последнему сообщению с задержкой
    setTimeout(() => {
        messageDiv.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }, 100);
}

function typeWriterEffect(element, text, baseSpeed = 15) {
    element.textContent = '';
    element.classList.add('typing-border');
    
    let i = 0;
    let isTypingComplete = false;
    
    function typeNextChar() {
        if (i >= text.length) {
            // Завершаем печатание
            isTypingComplete = true;
            
            // Добавляем класс для плавного исчезновения
            setTimeout(() => {
                element.classList.add('fade-out');
                
                // Полностью удаляем класс после завершения анимации
                setTimeout(() => {
                    element.classList.remove('typing-border', 'fade-out');
                }, 1000); // Время анимации fade-out
            }, 800); // Небольшая пауза перед началом исчезновения
            
            return;
        }
        
        const char = text.charAt(i);
        const currentText = text.substring(0, i + 1);
        element.textContent = currentText;
        i++;
        
        // Вычисляем динамическую скорость в зависимости от символа
        let nextDelay = baseSpeed;
        
        if (char === ' ') {
            // Пробелы печатаем быстрее
            nextDelay = baseSpeed * 0.4;
        } else if (char === '.' || char === '!' || char === '?') {
            // Знаки препинания - пауза для естественности
            nextDelay = baseSpeed * 2.2;
        } else if (char === ',' || char === ';' || char === ':') {
            // Запятые и другие знаки - средняя пауза
            nextDelay = baseSpeed * 1.3;
        } else if (char === '\n') {
            // Переносы строк - длинная пауза
            nextDelay = baseSpeed * 2.8;
        } else if (/[a-zA-Zа-яА-Я]/.test(char)) {
            // Буквы с небольшой вариацией для естественности
            nextDelay = baseSpeed + (Math.random() * 6 - 3);
        } else {
            // Другие символы
            nextDelay = baseSpeed * 0.8;
        }
        
        // Добавляем ускорение по мере печатания (более плавное)
        const progress = i / text.length;
        const speedCurve = 1 - (progress * progress * 0.3); // Квадратичное ускорение
        nextDelay *= speedCurve;
        
        // Минимальная и максимальная скорость
        nextDelay = Math.max(Math.min(nextDelay, baseSpeed * 3), 4);
        
        setTimeout(typeNextChar, nextDelay);
    }
    
    // Начинаем печатание с небольшой задержки для плавности
    setTimeout(typeNextChar, 150);
}

// Функция для парсинга markdown
function parseMarkdown(text) {
    // Экранируем HTML теги для безопасности
    text = text.replace(/</g, '&lt;').replace(/>/g, '&gt;');
    
    // Заголовки
    text = text.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    text = text.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    text = text.replace(/^# (.*$)/gim, '<h1>$1</h1>');
    
    // Жирный текст
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    text = text.replace(/__(.*?)__/g, '<strong>$1</strong>');
    
    // Курсив
    text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
    text = text.replace(/_(.*?)_/g, '<em>$1</em>');
    
    // Зачеркнутый текст
    text = text.replace(/~~(.*?)~~/g, '<del>$1</del>');
    
    // Инлайн код
    text = text.replace(/`([^`]+)`/g, '<code>$1</code>');
    
    // Блоки кода
    text = text.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');
    
    // Ссылки
    text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');
    
    // Списки (простая реализация)
    text = text.replace(/^\* (.+)$/gm, '<li>$1</li>');
    text = text.replace(/^- (.+)$/gm, '<li>$1</li>');
    text = text.replace(/^\d+\. (.+)$/gm, '<li>$1</li>');
    
    // Оборачиваем последовательные <li> в <ul>
    text = text.replace(/(<li>.*?<\/li>(?:\s*<li>.*?<\/li>)*)/gs, '<ul>$1</ul>');
    
    // Переносы строк
    text = text.replace(/\n\n/g, '</p><p>');
    text = text.replace(/\n/g, '<br>');
    
    // Оборачиваем в параграфы, если нет других блочных элементов
    if (!text.includes('<h1>') && !text.includes('<h2>') && !text.includes('<h3>') && 
        !text.includes('<ul>') && !text.includes('<pre>')) {
        text = '<p>' + text + '</p>';
    }
    
    return text;
}

function typeWriterEffectWithMarkdown(element, text, baseSpeed = null) {
    // Используем настройку скорости или значение по умолчанию
    if (baseSpeed === null) {
        baseSpeed = settings.messageSpeed || 12;
    }
    
    // Парсим markdown в HTML
    const htmlContent = parseMarkdown(text);
    
    // Если эффект печатания отключен, показываем текст сразу
    if (!settings.enableTypingEffect) {
        element.innerHTML = htmlContent;
        return;
    }
    
    element.innerHTML = '';
    element.classList.add('typing-border');
    
    // Создаем временный элемент для получения финального HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    
    // Получаем весь текст без HTML тегов для печатания
    const plainText = tempDiv.textContent || tempDiv.innerText || '';
    
    let i = 0;
    
    function typeNextChar() {
        if (i >= plainText.length) {
            // Завершаем печатание - показываем финальный HTML
            element.innerHTML = htmlContent;
            
            // Добавляем класс для плавного исчезновения эффекта
            setTimeout(() => {
                element.classList.add('fade-out');
                setTimeout(() => {
                    element.classList.remove('typing-border', 'fade-out');
                }, 1000);
            }, 800);
            
            return;
        }
        
        const char = plainText.charAt(i);
        const currentText = plainText.substring(0, i + 1);
        
        // Показываем текущий текст как обычный текст во время печатания
        element.textContent = currentText;
        i++;
        
        // Вычисляем задержку
        let nextDelay = baseSpeed;
        
        if (char === ' ') {
            nextDelay = baseSpeed * 0.4;
        } else if (char === '.' || char === '!' || char === '?') {
            nextDelay = baseSpeed * 2.2;
        } else if (char === ',' || char === ';' || char === ':') {
            nextDelay = baseSpeed * 1.3;
        } else if (char === '\n') {
            nextDelay = baseSpeed * 2.8;
        } else if (/[a-zA-Zа-яА-Я]/.test(char)) {
            nextDelay = baseSpeed + (Math.random() * 6 - 3);
        } else {
            nextDelay = baseSpeed * 0.8;
        }
        
        // Ускорение по мере печатания
        const progress = i / plainText.length;
        const speedCurve = 1 - (progress * progress * 0.3);
        nextDelay *= speedCurve;
        
        nextDelay = Math.max(Math.min(nextDelay, baseSpeed * 3), 4);
        
        setTimeout(typeNextChar, nextDelay);
    }
    
    // Начинаем печатание
    setTimeout(typeNextChar, 150);
}

function addTypingIndicator() {
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message ai typing';
    
    const thinkingTexts = [
        'Думаю...',
        'Анализирую...',
        'Обрабатываю...',
        'Генерирую ответ...'
    ];
    
    const randomText = thinkingTexts[Math.floor(Math.random() * thinkingTexts.length)];
    
    typingDiv.innerHTML = `
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <div class="thinking-text" style="
                font-size: 0.85rem; 
                color: var(--text-muted); 
                margin-top: 0.5rem; 
                opacity: 0.7;
            ">${randomText}</div>
        </div>
    `;

    elements.messagesContainer.appendChild(typingDiv);
    typingDiv.scrollIntoView({ behavior: 'smooth' });

    return typingDiv;
}

function removeTypingIndicator(indicator) {
    if (indicator && indicator.parentNode) {
        // Плавное исчезновение
        indicator.style.transition = 'all 0.3s cubic-bezier(0.25, 1, 0.5, 1)';
        indicator.style.opacity = '0';
        indicator.style.transform = 'translateY(-10px) scale(0.95)';
        
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.remove();
            }
        }, 300);
    }
}

function startNewChat() {
    // Сохраняем текущий чат перед созданием нового
    if (currentChatMessages.length > 0) {
        saveCurrentChat();
    }

    // Сбрасываем состояние
    currentChatId = null;
    currentChatMessages = [];
    elements.messagesContainer.innerHTML = '';
    elements.messagesContainer.classList.remove('active');
    elements.welcomeScreen.style.display = 'flex';
    elements.messageInput.focus();
}

function loadChatHistory() {
    const saved = localStorage.getItem('gemini-chat-history');
    if (saved) {
        chatHistory = JSON.parse(saved);
        renderChatHistory();
    }
}

function renderChatHistory() {
    elements.chatHistory.innerHTML = '';

    chatHistory.forEach(chat => {
        const chatItem = document.createElement('div');
        chatItem.className = 'chat-history-item';
        chatItem.dataset.chatId = chat.id;

        // Добавляем класс активного чата
        if (currentChatId === chat.id) {
            chatItem.classList.add('active');
        }

        chatItem.innerHTML = `
            <div class="chat-item-content">
                <div class="chat-item-title">${escapeHtml(chat.title)}</div>
                <div class="chat-item-date">${new Date(chat.date).toLocaleDateString()}</div>
            </div>
            <div class="chat-item-actions">
                <button class="chat-action-btn edit-btn" title="Переименовать" data-chat-id="${chat.id}">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="chat-action-btn export-btn" title="Экспорт" data-chat-id="${chat.id}">
                    <i class="fas fa-download"></i>
                </button>
                <button class="chat-action-btn delete-btn" title="Удалить" data-chat-id="${chat.id}">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        // Добавляем обработчик клика для загрузки чата
        const contentDiv = chatItem.querySelector('.chat-item-content');
        contentDiv.addEventListener('click', () => {
            loadChat(chat.id);
        });

        // Добавляем обработчики для кнопок действий
        const editBtn = chatItem.querySelector('.edit-btn');
        const exportBtn = chatItem.querySelector('.export-btn');
        const deleteBtn = chatItem.querySelector('.delete-btn');

        editBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            promptRenameChat(chat.id, chat.title);
        });

        exportBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            exportChat(chat.id);
        });

        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            confirmDeleteChat(chat.id, chat.title);
        });

        elements.chatHistory.appendChild(chatItem);
    });
}

// Улучшенная система уведомлений
let notificationContainer = null;
let notificationQueue = [];
let activeNotifications = [];

function initNotificationSystem() {
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.className = 'notification-container';
        notificationContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            max-width: 400px;
        `;
        document.body.appendChild(notificationContainer);
    }
}

function showNotification(message, type = 'info', options = {}) {
    initNotificationSystem();

    const notification = createNotification(message, type, options);

    // Добавляем в очередь
    notificationQueue.push(notification);

    // Обрабатываем очередь
    processNotificationQueue();
}

function createNotification(message, type, options = {}) {
    const {
        title = null,
        duration = 4000,
        persistent = false,
        actions = [],
        icon = null,
        sound = false
    } = options;

    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    // Определяем иконку по типу
    const typeIcons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    const notificationIcon = icon || typeIcons[type] || typeIcons.info;

    // Создаем структуру уведомления
    notification.innerHTML = `
        <div class="notification-icon">
            <i class="${notificationIcon}"></i>
        </div>
        <div class="notification-content">
            ${title ? `<div class="notification-title">${escapeHtml(title)}</div>` : ''}
            <div class="notification-message">${escapeHtml(message)}</div>
            ${actions.length > 0 ? createNotificationActions(actions) : ''}
        </div>
        <button class="notification-close" aria-label="Закрыть">
            <i class="fas fa-times"></i>
        </button>
        ${!persistent ? '<div class="notification-progress"></div>' : ''}
    `;

    // Добавляем обработчики событий
    setupNotificationEvents(notification, duration, persistent, actions, sound);

    return notification;
}

function createNotificationActions(actions) {
    const actionsHtml = actions.map(action => `
        <button class="notification-action ${action.primary ? 'primary' : ''}" data-action-id="${action.id}">
            ${action.icon ? `<i class="${action.icon}"></i>` : ''}
            <span>${escapeHtml(action.text)}</span>
        </button>
    `).join('');

    return `<div class="notification-actions">${actionsHtml}</div>`;
}

function setupNotificationEvents(notification, duration, persistent, actions, sound) {
    // Обработчик закрытия
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        hideNotification(notification);
    });

    // Обработчики действий
    actions.forEach(action => {
        const actionBtn = notification.querySelector(`[data-action-id="${action.id}"]`);
        if (actionBtn && action.handler) {
            actionBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                action.handler();
                if (action.closeOnClick !== false) {
                    hideNotification(notification);
                }
            });
        }
    });

    // Автоматическое скрытие
    if (!persistent && duration > 0) {
        const progressBar = notification.querySelector('.notification-progress');
        if (progressBar) {
            progressBar.style.animationDuration = `${duration}ms`;
        }

        setTimeout(() => {
            if (notification.parentNode) {
                hideNotification(notification);
            }
        }, duration);
    }

    // Звуковое уведомление
    if (sound) {
        playNotificationSound();
    }

    // Hover эффекты
    notification.addEventListener('mouseenter', () => {
        notification.classList.add('notification-hovered');
        const progressBar = notification.querySelector('.notification-progress');
        if (progressBar) {
            progressBar.style.animationPlayState = 'paused';
        }
    });

    notification.addEventListener('mouseleave', () => {
        notification.classList.remove('notification-hovered');
        const progressBar = notification.querySelector('.notification-progress');
        if (progressBar) {
            progressBar.style.animationPlayState = 'running';
        }
    });
}

function processNotificationQueue() {
    // Ограничиваем количество одновременных уведомлений
    const maxNotifications = 5;

    while (notificationQueue.length > 0 && activeNotifications.length < maxNotifications) {
        const notification = notificationQueue.shift();
        showNotificationElement(notification);
        activeNotifications.push(notification);
    }
}

function showNotificationElement(notification) {
    // Добавляем в контейнер
    notificationContainer.appendChild(notification);

    // Запускаем анимацию появления
    requestAnimationFrame(() => {
        notification.classList.add('notification-show');
    });
}

function hideNotification(notification) {
    if (!notification.parentNode) return;

    // Анимация исчезновения
    notification.classList.add('notification-hide');

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }

        // Удаляем из активных уведомлений
        const index = activeNotifications.indexOf(notification);
        if (index > -1) {
            activeNotifications.splice(index, 1);
        }

        // Обрабатываем очередь
        processNotificationQueue();
    }, 300);
}



function showNotificationsDemo() {
    // Базовое уведомление
    showNotification('Добро пожаловать в улучшенную систему уведомлений!', 'info', {
        title: 'Демонстрация',
        duration: 5000,
        sound: true
    });

    // Успешное уведомление с действиями
    setTimeout(() => {
        showNotification('Операция выполнена успешно', 'success', {
            title: 'Успех',
            duration: 6000,
            actions: [
                {
                    id: 'view-details',
                    text: 'Подробнее',
                    icon: 'fas fa-eye',
                    handler: () => {
                        showNotification('Детали операции загружены', 'info', {
                            title: 'Информация',
                            duration: 3000
                        });
                    }
                },
                {
                    id: 'share',
                    text: 'Поделиться',
                    icon: 'fas fa-share',
                    primary: true,
                    handler: () => {
                        showNotification('Ссылка скопирована в буфер обмена', 'success', {
                            duration: 2000
                        });
                    }
                }
            ]
        });
    }, 1500);

    // Предупреждение
    setTimeout(() => {
        showNotification('Внимание! Проверьте настройки перед продолжением', 'warning', {
            title: 'Предупреждение',
            duration: 7000,
            actions: [
                {
                    id: 'check-settings',
                    text: 'Проверить',
                    icon: 'fas fa-cog',
                    primary: true,
                    handler: () => {
                        openSettings();
                    }
                }
            ]
        });
    }, 3000);

    // Ошибка с возможностью повтора
    setTimeout(() => {
        showNotification('Не удалось подключиться к серверу', 'error', {
            title: 'Ошибка подключения',
            duration: 8000,
            sound: true,
            actions: [
                {
                    id: 'retry',
                    text: 'Повторить',
                    icon: 'fas fa-redo',
                    primary: true,
                    handler: () => {
                        showNotification('Повторное подключение...', 'info', {
                            duration: 2000
                        });
                    }
                },
                {
                    id: 'help',
                    text: 'Справка',
                    icon: 'fas fa-question-circle',
                    handler: () => {
                        showNotification('Справочная информация открыта', 'info', {
                            duration: 3000
                        });
                    }
                }
            ]
        });
    }, 4500);

    // Постоянное уведомление
    setTimeout(() => {
        showNotification('Это важное уведомление останется до закрытия', 'info', {
            title: 'Важная информация',
            persistent: true,
            icon: 'fas fa-star',
            actions: [
                {
                    id: 'understand',
                    text: 'Понятно',
                    icon: 'fas fa-check',
                    primary: true,
                    handler: () => {
                        showNotification('Спасибо за внимание!', 'success', {
                            duration: 2000
                        });
                    }
                }
            ]
        });
    }, 6000);
}

// Заглушки для функций управления чатами
function exportAllChats() {
    showNotification('Экспорт всех чатов', 'info');
}

function confirmClearAllChats() {
    showNotification('Подтверждение очистки всех чатов', 'warning');
}

function openSearchModal() {
    elements.searchModal.classList.add('active');
}

function closeSearchModal() {
    elements.searchModal.classList.remove('active');
}

function performSearch() {
    // Заглушка для поиска
    console.log('Поиск:', elements.searchInput.value);
}

function closeConfirmModal() {
    elements.confirmModal.classList.remove('active');
}

// Функции для измерения скорости печати
function handleTypingSpeedTracking() {
    const currentTime = Date.now();
    const currentLength = elements.messageInput.value.length;

    // Добавляем текущие данные в историю
    typingHistory.push({
        time: currentTime,
        length: currentLength
    });

    // Ограничиваем историю последними 10 секундами
    const tenSecondsAgo = currentTime - 10000;
    typingHistory = typingHistory.filter(entry => entry.time > tenSecondsAgo);

    // Обновляем отображение скорости
    updateTypingSpeedDisplay();

    // Сбрасываем таймер автоматического сброса
    if (typingSpeedTimer) {
        clearTimeout(typingSpeedTimer);
    }

    // Автоматически сбрасываем скорость через 3 секунды бездействия
    typingSpeedTimer = setTimeout(() => {
        resetTypingSpeed();
    }, 3000);
}

function updateTypingSpeedDisplay() {
    if (typingHistory.length < 2) {
        elements.speedValue.textContent = '0';
        return;
    }

    // Берем данные за последние 5 секунд для более точного расчета
    const currentTime = Date.now();
    const fiveSecondsAgo = currentTime - 5000;
    const recentHistory = typingHistory.filter(entry => entry.time > fiveSecondsAgo);

    if (recentHistory.length < 2) {
        elements.speedValue.textContent = '0';
        return;
    }

    // Находим самую раннюю и самую позднюю записи
    const earliest = recentHistory[0];
    const latest = recentHistory[recentHistory.length - 1];

    // Рассчитываем скорость (символы в секунду)
    const timeDiff = (latest.time - earliest.time) / 1000; // в секундах
    const charDiff = Math.abs(latest.length - earliest.length);

    if (timeDiff > 0) {
        const speed = Math.round(charDiff / timeDiff);
        elements.speedValue.textContent = speed.toString();

        // Добавляем визуальную обратную связь
        updateSpeedIndicatorColor(speed);
    } else {
        elements.speedValue.textContent = '0';
    }
}

function updateSpeedIndicatorColor(speed) {
    const indicator = elements.typingSpeedIndicator;

    // Убираем предыдущие классы
    indicator.classList.remove('speed-slow', 'speed-medium', 'speed-fast', 'speed-very-fast');

    // Добавляем класс в зависимости от скорости
    if (speed >= 8) {
        indicator.classList.add('speed-very-fast');
        // Активируем пульсацию курсора при очень быстрой печати
        setCursorPulse(true);
    } else if (speed >= 5) {
        indicator.classList.add('speed-fast');
        setCursorPulse(true);
    } else if (speed >= 2) {
        indicator.classList.add('speed-medium');
        setCursorPulse(false);
    } else if (speed > 0) {
        indicator.classList.add('speed-slow');
        setCursorPulse(false);
    } else {
        setCursorPulse(false);
    }
}

function resetTypingSpeed() {
    typingStartTime = null;
    lastTypingTime = null;
    typingHistory = [];
    elements.speedValue.textContent = '0';

    // Убираем цветовые классы
    elements.typingSpeedIndicator.classList.remove('speed-slow', 'speed-medium', 'speed-fast', 'speed-very-fast');

    // Выключаем пульсацию курсора
    setCursorPulse(false);

    if (typingSpeedTimer) {
        clearTimeout(typingSpeedTimer);
        typingSpeedTimer = null;
    }
}

// Вспомогательная функция для экранирования HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Функции для сохранения и управления чатами
function saveChatMessage(messageData) {
    // Если это первое сообщение пользователя в новом чате, создаем новый чат
    if (!currentChatId && messageData.type === 'user') {
        createNewChatEntry(messageData.content);
    }

    // Обновляем существующий чат
    if (currentChatId) {
        updateChatInHistory();
    }
}

function createNewChatEntry(firstMessage) {
    // Создаем ID для нового чата
    currentChatId = 'chat_' + Date.now();

    // Создаем заголовок чата из первого сообщения (первые 50 символов)
    const title = firstMessage.length > 50
        ? firstMessage.substring(0, 50) + '...'
        : firstMessage;

    // Создаем новую запись чата
    const newChat = {
        id: currentChatId,
        title: title,
        date: new Date().toISOString(),
        messages: [...currentChatMessages],
        lastUpdated: new Date().toISOString()
    };

    // Добавляем в начало истории
    chatHistory.unshift(newChat);

    // Сохраняем в localStorage
    saveChatHistory();

    // Обновляем отображение
    renderChatHistory();

    showNotification('Новый чат создан', 'success');
}

function updateChatInHistory() {
    if (!currentChatId) return;

    // Находим текущий чат в истории
    const chatIndex = chatHistory.findIndex(chat => chat.id === currentChatId);

    if (chatIndex !== -1) {
        // Обновляем сообщения и время последнего обновления
        chatHistory[chatIndex].messages = [...currentChatMessages];
        chatHistory[chatIndex].lastUpdated = new Date().toISOString();

        // Перемещаем чат в начало списка
        const updatedChat = chatHistory.splice(chatIndex, 1)[0];
        chatHistory.unshift(updatedChat);

        // Сохраняем в localStorage
        saveChatHistory();

        // Обновляем отображение
        renderChatHistory();
    }
}

function saveCurrentChat() {
    if (currentChatMessages.length === 0) return;

    if (!currentChatId) {
        // Если нет ID чата, создаем новый
        const firstUserMessage = currentChatMessages.find(msg => msg.type === 'user');
        if (firstUserMessage) {
            createNewChatEntry(firstUserMessage.content);
        }
    } else {
        // Обновляем существующий чат
        updateChatInHistory();
    }
}

function saveChatHistory() {
    try {
        localStorage.setItem('gemini-chat-history', JSON.stringify(chatHistory));
    } catch (error) {
        console.error('Ошибка сохранения истории чатов:', error);
        showNotification('Ошибка сохранения чата', 'error');
    }
}

function loadChat(chatId) {
    // Сохраняем текущий чат перед загрузкой нового
    if (currentChatMessages.length > 0 && currentChatId !== chatId) {
        saveCurrentChat();
    }

    // Находим чат в истории
    const chat = chatHistory.find(c => c.id === chatId);
    if (!chat) {
        showNotification('Чат не найден', 'error');
        return;
    }

    // Устанавливаем текущий чат
    currentChatId = chatId;
    currentChatMessages = [...chat.messages];

    // Очищаем контейнер сообщений
    elements.messagesContainer.innerHTML = '';

    // Загружаем сообщения
    chat.messages.forEach(message => {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${message.type}`;

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.textContent = message.content;

        messageDiv.appendChild(messageContent);
        elements.messagesContainer.appendChild(messageDiv);
    });

    // Показываем контейнер сообщений и скрываем экран приветствия
    elements.welcomeScreen.style.display = 'none';
    elements.messagesContainer.classList.add('active');

    // Прокручиваем к последнему сообщению
    if (elements.messagesContainer.lastChild) {
        elements.messagesContainer.lastChild.scrollIntoView({ behavior: 'smooth' });
    }

    showNotification('Чат загружен', 'success');
}

function deleteChat(chatId) {
    const chatIndex = chatHistory.findIndex(chat => chat.id === chatId);
    if (chatIndex === -1) {
        showNotification('Чат не найден', 'error');
        return;
    }

    // Удаляем чат из истории
    const deletedChat = chatHistory.splice(chatIndex, 1)[0];

    // Если удаляем текущий чат, сбрасываем состояние
    if (currentChatId === chatId) {
        currentChatId = null;
        currentChatMessages = [];
        elements.messagesContainer.innerHTML = '';
        elements.messagesContainer.classList.remove('active');
        elements.welcomeScreen.style.display = 'flex';
    }

    // Сохраняем изменения
    saveChatHistory();

    // Обновляем отображение
    renderChatHistory();

    showNotification(`Чат "${deletedChat.title}" удален`, 'success');
}

function exportChat(chatId) {
    const chat = chatHistory.find(c => c.id === chatId);
    if (!chat) {
        showNotification('Чат не найден', 'error');
        return;
    }

    // Формируем текст для экспорта
    let exportText = `Чат: ${chat.title}\n`;
    exportText += `Дата создания: ${new Date(chat.date).toLocaleString()}\n`;
    exportText += `Последнее обновление: ${new Date(chat.lastUpdated).toLocaleString()}\n\n`;

    chat.messages.forEach(message => {
        const sender = message.type === 'user' ? 'Пользователь' :
            message.type === 'ai' ? 'AI' : 'Система';
        const time = new Date(message.timestamp).toLocaleString();
        exportText += `[${time}] ${sender}: ${message.content}\n\n`;
    });

    // Создаем и скачиваем файл
    const blob = new Blob([exportText], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat_${chat.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('Чат экспортирован', 'success');
}

function renameChat(chatId, newTitle) {
    const chatIndex = chatHistory.findIndex(chat => chat.id === chatId);
    if (chatIndex === -1) {
        showNotification('Чат не найден', 'error');
        return;
    }

    // Обновляем название
    chatHistory[chatIndex].title = newTitle;
    chatHistory[chatIndex].lastUpdated = new Date().toISOString();

    // Сохраняем изменения
    saveChatHistory();

    // Обновляем отображение
    renderChatHistory();

    showNotification('Чат переименован', 'success');
}

// Вспомогательные функции
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function promptRenameChat(chatId, currentTitle) {
    const newTitle = prompt('Введите новое название чата:', currentTitle);
    if (newTitle && newTitle.trim() && newTitle.trim() !== currentTitle) {
        renameChat(chatId, newTitle.trim());
    }
}

function confirmDeleteChat(chatId, chatTitle) {
    if (confirm(`Вы уверены, что хотите удалить чат "${chatTitle}"?`)) {
        deleteChat(chatId);
    }
}

// Реализация функций управления всеми чатами
function exportAllChats() {
    if (chatHistory.length === 0) {
        showNotification('Нет чатов для экспорта', 'warning');
        return;
    }

    let exportText = `Экспорт всех чатов Gemini AI\n`;
    exportText += `Дата экспорта: ${new Date().toLocaleString()}\n`;
    exportText += `Количество чатов: ${chatHistory.length}\n\n`;
    exportText += '='.repeat(50) + '\n\n';

    chatHistory.forEach((chat, index) => {
        exportText += `ЧАТ ${index + 1}: ${chat.title}\n`;
        exportText += `Дата создания: ${new Date(chat.date).toLocaleString()}\n`;
        exportText += `Последнее обновление: ${new Date(chat.lastUpdated).toLocaleString()}\n`;
        exportText += `Количество сообщений: ${chat.messages.length}\n\n`;

        chat.messages.forEach(message => {
            const sender = message.type === 'user' ? 'Пользователь' :
                message.type === 'ai' ? 'AI' : 'Система';
            const time = new Date(message.timestamp).toLocaleString();
            exportText += `[${time}] ${sender}: ${message.content}\n\n`;
        });

        exportText += '-'.repeat(30) + '\n\n';
    });

    // Создаем и скачиваем файл
    const blob = new Blob([exportText], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `all_chats_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification(`Экспортировано ${chatHistory.length} чатов`, 'success');
}

function confirmClearAllChats() {
    if (chatHistory.length === 0) {
        showNotification('Нет чатов для удаления', 'warning');
        return;
    }

    const confirmed = confirm(`Вы уверены, что хотите удалить все чаты (${chatHistory.length})? Это действие нельзя отменить.`);
    if (confirmed) {
        clearAllChats();
    }
}

function clearAllChats() {
    // Очищаем историю чатов
    chatHistory = [];

    // Сбрасываем текущий чат
    currentChatId = null;
    currentChatMessages = [];

    // Очищаем интерфейс
    elements.messagesContainer.innerHTML = '';
    elements.messagesContainer.classList.remove('active');
    elements.welcomeScreen.style.display = 'flex';

    // Сохраняем изменения
    saveChatHistory();

    // Обновляем отображение
    renderChatHistory();

    showNotification('Все чаты удалены', 'success');
}

// Функция поиска в чатах
function performSearch() {
    const query = elements.searchInput.value.trim().toLowerCase();
    const resultsContainer = elements.searchResults;

    if (!query) {
        resultsContainer.innerHTML = '<p>Введите текст для поиска</p>';
        return;
    }

    const results = [];

    chatHistory.forEach(chat => {
        // Поиск в названии чата
        if (chat.title.toLowerCase().includes(query)) {
            results.push({
                type: 'title',
                chatId: chat.id,
                chatTitle: chat.title,
                match: chat.title,
                date: chat.date
            });
        }

        // Поиск в сообщениях
        chat.messages.forEach((message, messageIndex) => {
            if (message.content.toLowerCase().includes(query)) {
                results.push({
                    type: 'message',
                    chatId: chat.id,
                    chatTitle: chat.title,
                    match: message.content,
                    messageType: message.type,
                    messageIndex: messageIndex,
                    date: message.timestamp
                });
            }
        });
    });

    if (results.length === 0) {
        resultsContainer.innerHTML = '<p>Ничего не найдено</p>';
        return;
    }

    // Отображаем результаты
    resultsContainer.innerHTML = '';
    results.forEach(result => {
        const resultDiv = document.createElement('div');
        resultDiv.className = 'search-result-item';
        resultDiv.style.cssText = `
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all var(--transition);
        `;

        const typeLabel = result.type === 'title' ? 'Название чата' :
            result.messageType === 'user' ? 'Сообщение пользователя' : 'Ответ AI';

        const preview = result.match.length > 100
            ? result.match.substring(0, 100) + '...'
            : result.match;

        resultDiv.innerHTML = `
            <div style="font-size: 0.75rem; color: var(--text-muted); margin-bottom: 0.25rem;">
                ${typeLabel} • ${result.chatTitle} • ${new Date(result.date).toLocaleDateString()}
            </div>
            <div style="color: var(--text-primary);">${escapeHtml(preview)}</div>
        `;

        resultDiv.addEventListener('click', () => {
            loadChat(result.chatId);
            closeSearchModal();
        });

        resultDiv.addEventListener('mouseenter', () => {
            resultDiv.style.background = 'var(--bg-tertiary)';
            resultDiv.style.borderColor = 'var(--primary-color)';
        });

        resultDiv.addEventListener('mouseleave', () => {
            resultDiv.style.background = 'var(--bg-secondary)';
            resultDiv.style.borderColor = 'var(--border-color)';
        });

        resultsContainer.appendChild(resultDiv);
    });

    // Добавляем заголовок с количеством результатов
    const header = document.createElement('div');
    header.style.cssText = `
        padding: 0.5rem 0;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-secondary);
        border-bottom: 1px solid var(--border-color);
    `;
    header.textContent = `Найдено результатов: ${results.length}`;
    resultsContainer.insertBefore(header, resultsContainer.firstChild);
}

// Переменные для работы с файлами
let attachedFiles = [];
let fileInput = null;

// Создание скрытого input для файлов
function createFileInput() {
    if (!fileInput) {
        fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
        fileInput.multiple = true;
        fileInput.style.display = 'none';
        document.body.appendChild(fileInput);

        fileInput.addEventListener('change', handleFileSelect);
    }
    return fileInput;
}

// Открытие диалога выбора файлов
function openFileDialog() {
    const input = createFileInput();
    input.click();
}

// Обработка выбранных файлов
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    processFiles(files);
    event.target.value = ''; // Сброс значения для повторного выбора того же файла
}

// Обработка файлов
function processFiles(files) {
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length === 0) {
        showNotification('Пожалуйста, выберите изображения', 'warning');
        return;
    }

    // Проверка размера файлов (максимум 10MB на файл)
    const maxSize = 10 * 1024 * 1024; // 10MB
    const oversizedFiles = imageFiles.filter(file => file.size > maxSize);

    if (oversizedFiles.length > 0) {
        showNotification(`Файлы слишком большие (максимум 10MB): ${oversizedFiles.map(f => f.name).join(', ')}`, 'error');
        return;
    }

    // Добавляем файлы к прикрепленным
    imageFiles.forEach(file => {
        const fileData = {
            file: file,
            name: file.name,
            size: file.size,
            type: file.type,
            id: Date.now() + Math.random(),
            preview: null
        };

        attachedFiles.push(fileData);
        createFilePreview(fileData);
    });

    updateAttachedFilesDisplay();
    showNotification(`Прикреплено изображений: ${imageFiles.length}`, 'success');
}

// Создание превью файла
function createFilePreview(fileData) {
    if (fileData.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function (e) {
            fileData.preview = e.target.result;
            updateAttachedFilesDisplay();
        };
        reader.readAsDataURL(fileData.file);
    }
}

// Обновление отображения прикрепленных файлов
function updateAttachedFilesDisplay() {
    let container = document.querySelector('.attached-files-container');

    if (attachedFiles.length === 0) {
        if (container) {
            container.remove();
        }
        return;
    }

    if (!container) {
        container = document.createElement('div');
        container.className = 'attached-files-container';

        const inputPanel = document.querySelector('.input-panel');
        const inputContainer = document.querySelector('.input-container');
        inputContainer.insertBefore(container, inputContainer.firstChild);
    }

    container.innerHTML = `
        <div class="attached-files-header">
            <span><i class="fas fa-paperclip"></i> Прикрепленные файлы (${attachedFiles.length})</span>
            <button class="clear-files-btn" onclick="clearAllFiles()">
                <i class="fas fa-times"></i>
                Очистить все
            </button>
        </div>
        <div class="attached-files-list">
            ${attachedFiles.map(file => createFilePreviewHTML(file)).join('')}
        </div>
    `;

    // Добавляем обработчики для кнопок удаления
    container.querySelectorAll('.remove-file-btn').forEach(btn => {
        btn.addEventListener('click', function () {
            const fileId = this.dataset.fileId;
            removeFile(fileId);
        });
    });

    // Добавляем обработчики для просмотра изображений
    container.querySelectorAll('.file-preview-image img').forEach(img => {
        img.addEventListener('click', function () {
            const fileId = this.dataset.fileId;
            const file = attachedFiles.find(f => f.id == fileId);
            if (file && file.preview) {
                showImageModal(file.preview, file.name);
            }
        });
    });
}

// Создание HTML для превью файла
function createFilePreviewHTML(fileData) {
    const sizeFormatted = formatFileSize(fileData.size);

    return `
        <div class="file-preview">
            ${fileData.preview ?
            `<div class="file-preview-image">
                    <img src="${fileData.preview}" alt="${fileData.name}" data-file-id="${fileData.id}" style="cursor: pointer;">
                </div>` :
            `<div class="file-icon">
                    <i class="fas fa-image"></i>
                </div>`
        }
            <div class="file-info">
                <div class="file-name" title="${fileData.name}">${fileData.name}</div>
                <div class="file-size">${sizeFormatted}</div>
            </div>
            <button class="remove-file-btn" data-file-id="${fileData.id}" title="Удалить файл">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
}

// Форматирование размера файла
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    // Ограничиваем индекс размером массива
    const sizeIndex = Math.min(i, sizes.length - 1);
    
    return parseFloat((bytes / Math.pow(k, sizeIndex)).toFixed(2)) + ' ' + sizes[sizeIndex];
}

// Удаление файла
function removeFile(fileId) {
    attachedFiles = attachedFiles.filter(file => file.id != fileId);
    updateAttachedFilesDisplay();

    if (attachedFiles.length === 0) {
        showNotification('Все файлы удалены', 'info');
    }
}

// Очистка всех файлов
function clearAllFiles() {
    attachedFiles = [];
    updateAttachedFilesDisplay();
    showNotification('Все файлы удалены', 'info');
}

// Настройка Drag and Drop
function setupDragAndDrop() {
    const inputWrapper = document.querySelector('.input-wrapper');
    const messageInput = document.getElementById('messageInput');

    // Предотвращаем стандартное поведение браузера
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        inputWrapper.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Подсветка области при перетаскивании
    ['dragenter', 'dragover'].forEach(eventName => {
        inputWrapper.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        inputWrapper.addEventListener(eventName, unhighlight, false);
    });

    // Глобальные обработчики для курсора
    document.addEventListener('dragenter', () => {
        setCursorState('drag');
    });

    document.addEventListener('dragleave', (e) => {
        // Проверяем, что мы действительно покинули документ
        if (!e.relatedTarget) {
            setCursorState('default');
        }
    });

    document.addEventListener('drop', () => {
        setCursorState('default');
    });

    // Обработка сброса файлов
    inputWrapper.addEventListener('drop', handleDrop, false);

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight(e) {
        inputWrapper.classList.add('drag-over');
    }

    function unhighlight(e) {
        inputWrapper.classList.remove('drag-over');
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = Array.from(dt.files);
        processFiles(files);
    }
}

// Модальное окно для просмотра изображений
function showImageModal(imageSrc, imageName) {
    // Создаем модальное окно если его нет
    let modal = document.getElementById('imageModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'imageModal';
        modal.className = 'image-modal-overlay';
        modal.innerHTML = `
            <div class="image-modal-content">
                <div class="image-modal-header">
                    <h3 id="imageModalTitle">Просмотр изображения</h3>
                    <button class="image-modal-close" id="imageModalClose">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="image-modal-body">
                    <img id="imageModalImg" src="" alt="">
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Обработчики закрытия
        const closeBtn = modal.querySelector('#imageModalClose');
        closeBtn.addEventListener('click', closeImageModal);

        modal.addEventListener('click', function (e) {
            if (e.target === modal) {
                closeImageModal();
            }
        });

        // Закрытие по Escape
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape' && modal.classList.contains('active')) {
                closeImageModal();
            }
        });
    }

    // Устанавливаем изображение и заголовок
    const img = modal.querySelector('#imageModalImg');
    const title = modal.querySelector('#imageModalTitle');

    img.src = imageSrc;
    img.alt = imageName;
    title.textContent = imageName;

    // Показываем модальное окно
    modal.classList.add('active');
}

// Закрытие модального окна изображения
function closeImageModal() {
    const modal = document.getElementById('imageModal');
    if (modal) {
        modal.classList.remove('active');
    }
}

// Обновляем функцию отправки сообщения для работы с изображениями
const originalSendMessage = sendMessage;
sendMessage = async function () {
    const message = elements.messageInput.value.trim();
    if ((!message && attachedFiles.length === 0) || !settings.apiKey || isTyping) return;

    // Добавляем сообщение пользователя с файлами
    addMessageWithFiles(message, 'user', [...attachedFiles]);

    // Очищаем поле ввода и файлы
    elements.messageInput.value = '';
    const filesToSend = [...attachedFiles];
    attachedFiles = [];
    updateAttachedFilesDisplay();

    autoResizeTextarea(elements.messageInput);
    updateCharCounter();
    updateSendButton();

    // Скрываем экран приветствия
    elements.welcomeScreen.style.display = 'none';
    elements.messagesContainer.classList.add('active');

    // Показываем индикатор печати
    isTyping = true;
    setCursorPulse(true); // Включаем пульсацию курсора
    const typingIndicator = addTypingIndicator();

    try {
        const response = await callGeminiAPIWithImages(message, filesToSend);
        removeTypingIndicator(typingIndicator);
        addMessage(response, 'ai');
    } catch (error) {
        removeTypingIndicator(typingIndicator);
        addMessage(`Ошибка: ${error.message}`, 'error');
    }

    isTyping = false;
    setCursorPulse(false); // Выключаем пульсацию курсора
    updateSendButton();
};

// Добавление сообщения с файлами
function addMessageWithFiles(content, type, files = []) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    if (content) {
        messageContent.textContent = content;
    }

    // Добавляем файлы если есть
    if (files.length > 0) {
        const filesContainer = document.createElement('div');
        filesContainer.className = 'message-files';

        files.forEach(file => {
            const fileDiv = document.createElement('div');
            fileDiv.className = 'message-file';

            fileDiv.innerHTML = `
                ${file.preview ?
                    `<div class="message-file-image">
                        <img src="${file.preview}" alt="${file.name}" onclick="showImageModal('${file.preview}', '${file.name}')">
                    </div>` :
                    `<div class="message-file-icon">
                        <i class="fas fa-image"></i>
                    </div>`
                }
                <div class="message-file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${formatFileSize(file.size)}</div>
                </div>
            `;

            filesContainer.appendChild(fileDiv);
        });

        messageContent.appendChild(filesContainer);
    }

    messageDiv.appendChild(messageContent);
    elements.messagesContainer.appendChild(messageDiv);

    // Добавляем сообщение в текущий чат
    const messageData = {
        content: content,
        type: type,
        timestamp: new Date().toISOString(),
        files: files.map(f => ({
            name: f.name,
            size: f.size,
            type: f.type,
            preview: f.preview
        }))
    };

    currentChatMessages.push(messageData);
    saveChatMessage(messageData);

    // Прокрутка к последнему сообщению
    messageDiv.scrollIntoView({ behavior: 'smooth' });
}

// Вызов Gemini API с изображениями
async function callGeminiAPIWithImages(message, files = []) {
    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${settings.model}:generateContent`;

    // Подготавливаем части сообщения
    const parts = [];

    // Добавляем текст если есть
    if (message) {
        parts.push({ text: message });
    }

    // Добавляем изображения
    for (const file of files) {
        if (file.type.startsWith('image/')) {
            try {
                const base64Data = file.preview.split(',')[1]; // Убираем data:image/...;base64,
                parts.push({
                    inline_data: {
                        mime_type: file.type,
                        data: base64Data
                    }
                });
            } catch (error) {
                console.error('Ошибка обработки изображения:', error);
            }
        }
    }

    const requestBody = {
        contents: [{
            parts: parts
        }],
        generationConfig: {
            temperature: settings.temperature,
            maxOutputTokens: settings.maxTokens
        }
    };

    const response = await fetch(`${API_URL}?key=${settings.apiKey}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error?.message || `HTTP ${response.status}`);
    }

    const data = await response.json();

    if (!data.candidates || data.candidates.length === 0) {
        throw new Error('Нет ответа от AI');
    }

    return data.candidates[0].content.parts[0].text;
}

// Обновляем функцию updateSendButton для учета файлов
const originalUpdateSendButton = updateSendButton;
updateSendButton = function () {
    const hasText = elements.messageInput.value.trim().length > 0;
    const hasFiles = attachedFiles.length > 0;
    const hasApiKey = settings.apiKey.length > 0;

    elements.sendBtn.disabled = (!hasText && !hasFiles) || !hasApiKey || isTyping;
};