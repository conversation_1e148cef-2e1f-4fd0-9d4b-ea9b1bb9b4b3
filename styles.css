/* Минималистичные переменные */
:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;

    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-chat: #ffffff;
    --bg-message-user: var(--primary-color);
    --bg-message-ai: #f8fafc;

    --text-primary: #0f172a;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-inverse: #ffffff;

    --border-color: #e2e8f0;
    --border-radius: 8px;
    --border-radius-lg: 12px;

    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    --transition-fast: 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition: 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-slow: 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-smooth: 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-hover: 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

    /* Переменные для анимации переключения темы */
    --theme-transition-duration: 0.8s;
    --theme-transition-easing: cubic-bezier(0.25, 0.46, 0.45, 0.94);

    /* Настраиваемый размер шрифта */
    --message-font-size: 16px;
}

/* Темная тема */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-chat: #0f172a;
    --bg-message-ai: #1e293b;

    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;

    --border-color: #334155;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.4);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Универсальные плавные переходы для всех интерактивных элементов */
button,
input,
textarea,
select,
a,
.clickable,
[role="button"],
[tabindex]:not([tabindex="-1"]) {
    transition: all var(--transition-hover);
    will-change: transform, background-color, border-color, box-shadow, color, opacity;
}

/* Оптимизация производительности анимаций */
.message,
.chat-history-item,
.suggestion-card,
.notification,
.modal-content,
.file-preview,
.attached-file-item,
.theme-toggle,
.new-chat-btn,
.attach-btn,
.send-btn {
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
}

/* Предотвращение дрожания текста при анимациях */
.message-content,
.chat-item-title,
.suggestion-card span,
.notification-message {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Плавные переходы для псевдоэлементов */
*::before,
*::after {
    transition: all var(--transition-hover);
}

/* Уважение к настройкам пользователя по уменьшению движения */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .theme-wave-effect,
    .typing-dots,
    .spinner {
        animation: none !important;
    }
}

/* Отключение анимаций через настройки */
.no-animations *,
.no-animations *::before,
.no-animations *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    transform: none !important;
}

.no-animations .theme-wave-effect,
.no-animations .typing-dots,
.no-animations .spinner,
.no-animations .typing-border {
    animation: none !important;
}

.no-animations .message {
    opacity: 1 !important;
    transform: none !important;
}

.no-animations .notification {
    transform: translateX(0) !important;
    opacity: 1 !important;
}

/* Улучшенная плавность для элементов с градиентами */
.theme-toggle,
.new-chat-btn,
.send-btn {
    transition:
        transform var(--transition-hover),
        background var(--transition-hover),
        background-color var(--transition-hover),
        border-color var(--transition-hover),
        box-shadow var(--transition-hover),
        color var(--transition-hover),
        opacity var(--transition-hover);
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    transition:
        background-color var(--theme-transition-duration) var(--theme-transition-easing),
        color var(--theme-transition-duration) var(--theme-transition-easing);
    position: relative;
    overflow-x: hidden;
}

/* Анимация переключения темы с волновым эффектом */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    z-index: -1;
    transition: background-color var(--theme-transition-duration) var(--theme-transition-easing);
}

/* Волновой эффект при переключении темы */
.theme-wave-effect {
    position: fixed;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: var(--bg-primary);
    transform: translate(-50%, -50%);
    z-index: 9999;
    pointer-events: none;
    opacity: 0;
}

.theme-wave-effect.active {
    animation: themeWaveExpand 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes themeWaveExpand {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }

    50% {
        opacity: 0.8;
    }

    100% {
        width: 300vw;
        height: 300vh;
        opacity: 0;
    }
}

/* Контейнер приложения */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    transition: all var(--theme-transition-duration) var(--theme-transition-easing);
}

/* Плавные переходы для всех основных элементов */
.app-header,
.sidebar,
.chat-area,
.input-panel,
.modal-overlay,
.notification-container {
    transition:
        background-color var(--theme-transition-duration) var(--theme-transition-easing),
        border-color var(--theme-transition-duration) var(--theme-transition-easing),
        color var(--theme-transition-duration) var(--theme-transition-easing),
        box-shadow var(--theme-transition-duration) var(--theme-transition-easing);
}

/* Дополнительные плавные переходы для интерактивных элементов */
.message-content,
.file-preview,
.attached-file-item,
.search-result-item,
.modal-content,
.notification {
    transition: all var(--transition-hover);
}

/* Улучшенные переходы для элементов с трансформациями */
.logo-icon,
.welcome-icon,
.theme-toggle,
.new-chat-btn,
.chat-history-item,
.suggestion-card,
.attach-btn,
.send-btn,
.chat-control-btn,
.chat-action-btn {
    transition:
        transform var(--transition-hover),
        background-color var(--transition-hover),
        border-color var(--transition-hover),
        box-shadow var(--transition-hover),
        color var(--transition-hover);
}

/* Заголовок */
.app-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(8px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.logo-icon:hover {
    transform: scale(1.08) rotate(2deg);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.logo-icon:hover::before {
    left: 100%;
}

.app-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.header-controls {
    display: flex;
    gap: 0.5rem;
}

.theme-toggle,
.notifications-demo-btn,
.settings-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.theme-toggle::before,
.notifications-demo-btn::before,
.settings-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(37, 99, 235, 0.15) 0%, transparent 70%);
    transition: all var(--transition-smooth);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.theme-toggle:hover::before,
.notifications-demo-btn:hover::before,
.settings-btn:hover::before {
    width: 60px;
    height: 60px;
}

.theme-toggle:hover,
.notifications-demo-btn:hover,
.settings-btn:hover {
    background: var(--bg-tertiary);
    color: var(--primary-color);
    transform: translateY(-1px) scale(1.02);
    box-shadow: var(--shadow-md);
}

.theme-toggle:active,
.notifications-demo-btn:active,
.settings-btn:active {
    transform: translateY(0) scale(0.98);
    transition: all var(--transition-fast);
}

/* Специальные стили для кнопки переключения темы */
.theme-toggle {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
}

.theme-toggle::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
            rgba(37, 99, 235, 0.3) 0%,
            rgba(6, 182, 212, 0.3) 50%,
            rgba(245, 158, 11, 0.3) 100%);
    border-radius: var(--border-radius);
    z-index: -1;
    opacity: 0;
    transition: opacity var(--transition-smooth);
}

.theme-toggle:hover::after {
    opacity: 1;
}

.theme-toggle i {
    transition: all var(--transition-bounce);
    position: relative;
    z-index: 1;
}

.theme-toggle:hover i {
    transform: rotate(180deg) scale(1.1);
}

.theme-toggle.switching i {
    animation: themeIconSpin 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes themeIconSpin {
    0% {
        transform: rotate(0deg) scale(1);
    }

    25% {
        transform: rotate(90deg) scale(1.2);
    }

    50% {
        transform: rotate(180deg) scale(0.8);
    }

    75% {
        transform: rotate(270deg) scale(1.1);
    }

    100% {
        transform: rotate(360deg) scale(1);
    }
}

/* Дополнительные эффекты для темной темы */
[data-theme="dark"] .theme-toggle {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.1);
}

[data-theme="dark"] .theme-toggle::after {
    background: linear-gradient(135deg,
            rgba(245, 158, 11, 0.4) 0%,
            rgba(251, 191, 36, 0.3) 50%,
            rgba(37, 99, 235, 0.2) 100%);
}

[data-theme="dark"] .theme-toggle:hover {
    box-shadow: 0 0 30px rgba(245, 158, 11, 0.2);
}

/* Улучшенные стили для кнопки "Новый чат" в темной теме */
[data-theme="dark"] .new-chat-btn {
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}

[data-theme="dark"] .new-chat-btn:hover {
    box-shadow: 0 8px 30px rgba(37, 99, 235, 0.3);
}

/* Основной контент */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Боковая панель */
.sidebar {
    width: 300px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-header h3 {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.new-chat-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
}

.new-chat-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.new-chat-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.new-chat-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.25);
}

.new-chat-btn:hover::before {
    opacity: 1;
}

.new-chat-btn:hover::after {
    width: 120px;
    height: 120px;
}

.new-chat-btn:hover i {
    transform: rotate(90deg) scale(1.1);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.new-chat-btn:active {
    transform: translateY(-1px) scale(0.98);
    transition: all 0.15s ease;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.2);
}

.new-chat-btn i {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-controls {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.chat-management-buttons {
    display: flex;
    gap: 0.5rem;
}

.chat-control-btn {
    flex: 1;
    padding: 0.5rem;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: all var(--transition-smooth);
    min-height: 36px;
    position: relative;
    overflow: hidden;
}

.chat-control-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(37, 99, 235, 0.1) 0%, transparent 70%);
    transition: all var(--transition-smooth);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.chat-control-btn:hover::before {
    width: 80px;
    height: 80px;
}

.chat-control-btn:hover {
    background: var(--bg-primary);
    color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px) scale(1.02);
    box-shadow: var(--shadow-sm);
}

.chat-control-btn#exportAllBtn:hover {
    color: var(--success-color);
    border-color: var(--success-color);
}

.chat-control-btn#clearAllBtn:hover {
    color: var(--error-color);
    border-color: var(--error-color);
}

.chat-control-btn#searchChatsBtn:hover {
    color: var(--accent-color);
    border-color: var(--accent-color);
}

.chat-history {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.chat-history-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.chat-history-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.05), transparent);
    transition: left var(--transition-slow);
}

.chat-history-item:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
    transform: translateX(2px) scale(1.005);
    background: var(--bg-secondary);
}

.chat-history-item:hover::before {
    left: 100%;
}

.chat-history-item.active {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
    transform: translateX(2px);
}

.chat-history-item.active .chat-item-title {
    color: var(--primary-color);
    font-weight: 600;
}

.chat-item-content {
    flex: 1;
    min-width: 0;
    margin-right: 0.5rem;
}

.chat-item-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-primary);
}

.chat-item-date {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.chat-item-actions {
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity var(--transition);
}

.chat-history-item:hover .chat-item-actions {
    opacity: 1;
}

.chat-action-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all var(--transition);
}

.chat-action-btn:hover {
    transform: scale(1.1);
}

.chat-action-btn.edit-btn:hover {
    background: var(--accent-color);
    color: white;
}

.chat-action-btn.export-btn:hover {
    background: var(--success-color);
    color: white;
}

.chat-action-btn.delete-btn:hover {
    background: var(--error-color);
    color: white;
}

/* Область чата */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-chat);
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    display: flex;
    flex-direction: column;
}

/* Экран приветствия */
.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    opacity: 0;
    animation: welcomeEntrance 1s var(--transition-smooth) forwards;
}

@keyframes welcomeEntrance {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }

    60% {
        opacity: 0.8;
        transform: translateY(-5px) scale(1.01);
    }

    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.welcome-content {
    text-align: center;
    max-width: 600px;
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: white;
    font-size: 2rem;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
    animation: iconFloat 4s ease-in-out infinite;
}

.welcome-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all var(--transition-slow);
    opacity: 0;
}

.welcome-icon:hover {
    transform: scale(1.08) rotate(5deg);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.welcome-icon:hover::before {
    animation: shimmer 1s ease-in-out;
    opacity: 1;
}

@keyframes iconFloat {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-8px);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }

    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

.welcome-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.welcome-content p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
}

.suggestion-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.suggestion-card {
    padding: 1.5rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-smooth);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.suggestion-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(6, 182, 212, 0.05) 100%);
    opacity: 0;
    transition: opacity var(--transition-smooth);
}

.suggestion-card:hover::before {
    opacity: 1;
}

.suggestion-card:hover {
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.12);
    border-color: var(--primary-color);
    background: var(--bg-primary);
}

.suggestion-card:active {
    transform: translateY(-2px) scale(0.98);
    transition: all var(--transition-fast);
}

.suggestion-card i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    transition: all var(--transition-smooth);
    z-index: 1;
    position: relative;
}

.suggestion-card:hover i {
    transform: scale(1.1) rotate(5deg);
    color: var(--accent-color);
}

.suggestion-card span {
    font-weight: 500;
    color: var(--text-primary);
    transition: all var(--transition-smooth);
    z-index: 1;
    position: relative;
}

.suggestion-card:hover span {
    color: var(--primary-color);
    transform: translateY(-1px);
}

/* Контейнер сообщений */
.messages-container {
    display: none;
    flex-direction: column;
    gap: 1rem;
    padding-bottom: 2rem;
}

.messages-container.active {
    display: flex;
}

/* Сообщения */
.message {
    display: flex;
    margin-bottom: 1rem;
    opacity: 0;
    transform: translateY(20px);
    animation: messageSlideIn 0.5s var(--transition-smooth) forwards;
}

.message.user {
    justify-content: flex-end;
}

.message.ai {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 1rem 1.25rem;
    border-radius: var(--border-radius-lg);
    word-wrap: break-word;
    line-height: 1.6;
    font-size: var(--message-font-size);
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.message.user .message-content {
    background: var(--bg-message-user);
    color: var(--text-inverse);
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2);
}

.message.user .message-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-slow);
}

.message.user .message-content:hover::before {
    left: 100%;
}

.message.ai .message-content {
    background: var(--bg-message-ai);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    transition: all var(--transition-smooth);
}

.message.ai .message-content:hover {
    box-shadow: var(--shadow-sm);
    border-color: rgba(37, 99, 235, 0.2);
    transform: translateY(-1px);
}

.message.error .message-content {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border: 1px solid var(--error-color);
    animation: errorShake 0.5s ease-in-out;
}

@keyframes messageSlideIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes errorShake {

    0%,
    100% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-5px);
    }

    75% {
        transform: translateX(5px);
    }
}

/* Индикатор печати - плавная анимация */
.message.typing .message-content {
    padding: 1rem 1.5rem;
    position: relative;
    background: var(--bg-message-ai);
    border: 1px solid rgba(37, 99, 235, 0.2);
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.1);
    animation: gentleGlow 3s ease-in-out infinite;
}

@keyframes gentleGlow {

    0%,
    100% {
        box-shadow: 0 0 20px rgba(37, 99, 235, 0.1);
    }

    50% {
        box-shadow: 0 0 30px rgba(37, 99, 235, 0.2);
    }
}

.typing-dots {
    display: flex;
    gap: 0.4rem;
    align-items: center;
    justify-content: flex-start;
}

.typing-dots span {
    width: 10px;
    height: 10px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: smoothTypingDot 1.4s infinite ease-in-out;
    box-shadow: 0 0 10px rgba(37, 99, 235, 0.3);
}

.typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0s;
}

@keyframes smoothTypingDot {

    0%,
    80%,
    100% {
        transform: scale(0.7);
        opacity: 0.4;
    }

    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Убираем лишнюю точку */

/* Простая анимация для текста "думания" */
.thinking-text {
    font-style: italic;
}

/* Красивое переливание обводки во время печатания - синие оттенки */
.typing-border {
    position: relative;
    border: 2px solid transparent;
    background: linear-gradient(var(--bg-message-ai), var(--bg-message-ai)) padding-box,
        linear-gradient(90deg,
            #1e40af,
            /* темно-синий */
            #2563eb,
            /* синий */
            #3b82f6,
            /* светло-синий */
            #60a5fa,
            /* голубой */
            #1e40af
            /* темно-синий */
        ) border-box;
    background-size: 100% 100%, 500% 100%;
    animation: borderFlow 3s ease-in-out infinite;
}

@keyframes borderFlow {
    0% {
        background-position: 0% 0%, 0% 0%;
        box-shadow:
            0 0 20px rgba(30, 64, 175, 0.2),
            inset 0 0 20px rgba(30, 64, 175, 0.08);
    }

    25% {
        background-position: 0% 0%, 100% 0%;
        box-shadow:
            0 0 25px rgba(37, 99, 235, 0.25),
            inset 0 0 25px rgba(37, 99, 235, 0.1);
    }

    50% {
        background-position: 0% 0%, 200% 0%;
        box-shadow:
            0 0 30px rgba(59, 130, 246, 0.3),
            inset 0 0 30px rgba(59, 130, 246, 0.12);
    }

    75% {
        background-position: 0% 0%, 300% 0%;
        box-shadow:
            0 0 25px rgba(96, 165, 250, 0.25),
            inset 0 0 25px rgba(96, 165, 250, 0.1);
    }

    100% {
        background-position: 0% 0%, 500% 0%;
        box-shadow:
            0 0 20px rgba(30, 64, 175, 0.2),
            inset 0 0 20px rgba(30, 64, 175, 0.08);
    }
}

/* Дополнительное свечение текста во время печатания - синее */
.typing-border {
    color: var(--text-primary);
    text-shadow: 0 0 2px rgba(59, 130, 246, 0.4);
}

/* Плавное исчезновение свечения */
.typing-border.fade-out {
    animation: borderFadeOut 1s ease-out forwards;
}

@keyframes borderFadeOut {
    0% {
        border-color: transparent;
        box-shadow:
            0 0 30px rgba(59, 130, 246, 0.3),
            inset 0 0 30px rgba(59, 130, 246, 0.12);
        text-shadow: 0 0 2px rgba(59, 130, 246, 0.4);
    }

    100% {
        border-color: transparent;
        box-shadow:
            0 0 0px rgba(59, 130, 246, 0),
            inset 0 0 0px rgba(59, 130, 246, 0);
        text-shadow: none;
        background: var(--bg-message-ai);
    }
}

/* Плавное появление текста */
.message-content.typing-in {
    opacity: 0;
    transform: translateY(5px);
    transition: all 0.3s ease;
}

.message-content.typing-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Анимации для статуса AI - упрощенные */
.status-indicator {
    transition: all 0.3s ease;
    display: inline-block;
}

.status-indicator.ready {
    animation: statusReady 2s ease-in-out infinite;
}

.status-indicator.thinking {
    animation: statusThinking 1s ease-in-out infinite;
}

.status-indicator.warning {
    animation: statusWarning 1.5s ease-in-out infinite;
}

@keyframes statusReady {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

@keyframes statusThinking {

    0%,
    100% {
        opacity: 0.8;
    }

    50% {
        opacity: 1;
    }
}

@keyframes statusWarning {

    0%,
    100% {
        opacity: 0.6;
    }

    50% {
        opacity: 1;
    }
}

/* Простые эффекты для сообщения AI */
.message.ai .message-content {
    position: relative;
}

/* Стили для markdown элементов в сообщениях */
.message-content h1,
.message-content h2,
.message-content h3 {
    margin: 0.5rem 0;
    font-weight: 600;
    line-height: 1.3;
}

.message-content h1 {
    font-size: 1.5rem;
    color: var(--primary-color);
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 0.25rem;
}

.message-content h2 {
    font-size: 1.3rem;
    color: var(--primary-color);
}

.message-content h3 {
    font-size: 1.1rem;
    color: var(--text-primary);
}

.message-content strong {
    font-weight: 600;
    color: var(--text-primary);
}

.message-content em {
    font-style: italic;
    color: var(--text-secondary);
}

.message-content del {
    text-decoration: line-through;
    color: var(--text-muted);
}

.message-content code {
    background: var(--bg-tertiary);
    color: var(--primary-color);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    border: 1px solid var(--border-color);
}

.message-content pre {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 0.5rem 0;
    overflow-x: auto;
    position: relative;
}

.message-content pre code {
    background: none;
    border: none;
    padding: 0;
    color: var(--text-primary);
    font-size: 0.85rem;
    line-height: 1.4;
}

.message-content ul,
.message-content ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.message-content li {
    margin: 0.25rem 0;
    line-height: 1.5;
}

.message-content ul li {
    list-style-type: disc;
}

.message-content ol li {
    list-style-type: decimal;
}

.message-content a {
    color: var(--primary-color);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all var(--transition);
}

.message-content a:hover {
    border-bottom-color: var(--primary-color);
    color: var(--primary-hover);
}

.message-content p {
    margin: 0.5rem 0;
    line-height: 1.6;
}

.message-content p:first-child {
    margin-top: 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

/* Специальные стили для темной темы */
[data-theme="dark"] .message-content code {
    background: var(--bg-primary);
    color: var(--accent-color);
}

[data-theme="dark"] .message-content pre {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .message-content h1 {
    border-bottom-color: var(--border-color);
}

/* Панель ввода */
.input-panel {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
}

.input-container {
    max-width: 1000px;
    margin: 0 auto;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 16px;
    padding: 1rem 1.25rem;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.input-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.02) 0%, rgba(6, 182, 212, 0.02) 100%);
    opacity: 0;
    transition: opacity var(--transition-smooth);
    pointer-events: none;
}

.input-wrapper:hover {
    border-color: rgba(37, 99, 235, 0.4);
    transform: translateY(-0.5px);
    box-shadow: 0 3px 10px rgba(37, 99, 235, 0.06);
}

.input-wrapper:hover::before {
    opacity: 1;
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1), 0 6px 20px rgba(37, 99, 235, 0.12);
    transform: translateY(-1px);
}

.input-wrapper:focus-within::before {
    opacity: 1;
}

#messageInput {
    flex: 1;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 1rem;
    font-family: inherit;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.6;
}

#messageInput::placeholder {
    color: var(--text-muted);
}

.input-actions {
    display: flex;
    gap: 0.5rem;
    margin-left: 1rem;
}

.attach-btn,
.send-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.attach-btn::before,
.send-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    transition: all var(--transition-smooth);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.attach-btn:hover::before,
.send-btn:hover::before {
    width: 50px;
    height: 50px;
}

.attach-btn {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.attach-btn:hover {
    background: var(--bg-primary);
    color: var(--primary-color);
    transform: translateY(-0.5px) scale(1.02);
    box-shadow: var(--shadow-sm);
}

.attach-btn:active {
    transform: translateY(0) scale(0.95);
    transition: all var(--transition-fast);
}

.send-btn {
    background: var(--primary-color);
    color: white;
}

.send-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-0.5px) scale(1.03);
    box-shadow: 0 3px 10px rgba(37, 99, 235, 0.25);
}

.send-btn:active {
    transform: translateY(0) scale(0.95);
    transition: all var(--transition-fast);
}

.send-btn:disabled {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.send-btn:disabled::before {
    display: none;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.char-counter {
    padding: 0.25rem 0.5rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    font-size: 0.75rem;
}

.char-counter.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.char-counter.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.typing-speed-indicator,
.ai-status {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    transition: all var(--transition-smooth);
}

/* Прикрепленные файлы */
.attached-files-container {
    margin-bottom: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1rem;
    transition: all var(--transition-smooth);
    max-width: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.attached-files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.clear-files-btn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.75rem;
    transition: all var(--transition);
}

.clear-files-btn:hover {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

.attached-files-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.file-preview {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all var(--transition-smooth);
    position: relative;
    max-width: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.file-preview:hover {
    box-shadow: var(--shadow-sm);
    border-color: var(--primary-color);
}

.file-preview-image {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius);
    overflow: hidden;
    flex-shrink: 0;
}

.file-preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    color: var(--primary-color);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0.25rem;
}

.file-size {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.file-content-preview {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    line-height: 1.3;
}

.remove-file-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-muted);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all var(--transition);
    flex-shrink: 0;
}

.remove-file-btn:hover {
    background: var(--error-color);
    color: white;
    transform: scale(1.1);
}

/* Файлы в сообщениях */
.message-files {
    margin-top: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.message-file {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    transition: all var(--transition);
}

.message.user .message-file {
    background: rgba(255, 255, 255, 0.1);
}

.message.ai .message-file {
    background: var(--bg-tertiary);
}

.message-file:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.message-file-image {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    overflow: hidden;
    flex-shrink: 0;
}

.message-file-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
    transition: all var(--transition);
}

.message-file-image img:hover {
    transform: scale(1.05);
}

.message-file-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    color: var(--primary-color);
    font-size: 1.2rem;
    flex-shrink: 0;
}

.message.user .message-file-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.message-file-info {
    flex: 1;
    min-width: 0;
}

.message-file .file-name {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.125rem;
}

.message.user .message-file .file-name {
    color: rgba(255, 255, 255, 0.9);
}

.message-file .file-size {
    font-size: 0.75rem;
    opacity: 0.7;
}

.message.user .message-file .file-size {
    color: rgba(255, 255, 255, 0.7);
}

/* Drag and Drop зона */
.input-wrapper.drag-over {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    transform: translateY(-2px);
}

.input-wrapper.drag-over::before {
    opacity: 1;
}

/* Модальное окно для изображений */
.image-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-smooth);
    backdrop-filter: blur(0px);
}

.image-modal-overlay.active {
    opacity: 1;
    visibility: visible;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
}

.image-modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.8) translateY(20px);
    transition: all var(--transition-smooth);
    opacity: 0;
}

.image-modal-overlay.active .image-modal-content {
    transform: scale(1) translateY(0);
    opacity: 1;
}

.image-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.image-modal-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 400px;
}

.image-modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition);
    flex-shrink: 0;
}

.image-modal-close:hover {
    background: var(--error-color);
    color: white;
    transform: scale(1.1);
}

.image-modal-body {
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: calc(90vh - 80px);
}

.image-modal-body img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
}

/* Поиск в чатах */
.search-results {
    max-height: 400px;
    overflow-y: auto;
    margin-top: 1rem;
}

.search-no-results {
    text-align: center;
    color: var(--text-muted);
    padding: 2rem;
    font-style: italic;
}

.search-results-header {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.search-results-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.search-result-item {
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition);
}

.search-result-item:hover {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.search-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.search-result-chat {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.search-result-type {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.search-result-type.user {
    background: var(--primary-color);
    color: white;
}

.search-result-type.ai {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.search-result-content {
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.search-result-content mark {
    background: var(--warning-color);
    color: white;
    padding: 0.125rem 0.25rem;
    border-radius: 3px;
    font-weight: 500;
}

.search-result-date {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Подсветка найденного сообщения */
.message.highlighted {
    animation: messageHighlight 2s ease-in-out;
}

@keyframes messageHighlight {
    0% {
        background: rgba(37, 99, 235, 0.2);
        transform: scale(1.02);
    }

    50% {
        background: rgba(37, 99, 235, 0.1);
        transform: scale(1.01);
    }

    100% {
        background: transparent;
        transform: scale(1);
    }
}

/* Улучшенные стили уведомлений */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-width: 400px;
}

.notification {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    pointer-events: auto;
    transform: translateX(100%);
    opacity: 0;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
    max-width: 100%;
}

.notification.notification-show {
    transform: translateX(0);
    opacity: 1;
}

.notification.notification-hide {
    transform: translateX(100%);
    opacity: 0;
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-error {
    border-left: 4px solid var(--error-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-info {
    border-left: 4px solid var(--accent-color);
}

.notification-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 0.875rem;
}

.notification-success .notification-icon {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.notification-error .notification-icon {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.notification-warning .notification-icon {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.notification-info .notification-icon {
    background: rgba(6, 182, 212, 0.1);
    color: var(--accent-color);
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
    word-wrap: break-word;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.notification-action {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all var(--transition);
}

.notification-action:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.notification-action.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.notification-action.primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.notification-close {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all var(--transition);
    font-size: 0.75rem;
}

.notification-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: scale(1.1);
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--primary-color);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    animation: notificationProgress linear;
    transform-origin: left;
}

@keyframes notificationProgress {
    from {
        transform: scaleX(1);
    }

    to {
        transform: scaleX(0);
    }
}

.notification-hovered .notification-progress {
    animation-play-state: paused;
}

/* Стили для кнопок резервного копирования */
.backup-controls {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.backup-controls button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem;
    font-size: 0.875rem;
}

/* Модальные окна */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-smooth);
    backdrop-filter: blur(0px);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all var(--transition-smooth);
    opacity: 0;
}

.modal-overlay.active .modal-content {
    transform: scale(1) translateY(0);
    opacity: 1;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition);
}

.close-btn:hover {
    background: var(--error-color);
    color: white;
    transform: scale(1.1);
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.btn-primary,
.btn-secondary,
.btn-danger {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: all var(--transition-smooth);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.setting-group input,
.setting-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition);
}

.setting-group input:focus,
.setting-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.setting-group small {
    display: block;
    color: var(--text-muted);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    line-height: 1.4;
}

.temperature-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.temperature-container input[type="range"] {
    flex: 1;
}

.temperature-value {
    font-weight: 600;
    color: var(--primary-color);
    min-width: 2rem;
    text-align: center;
}

.slider-labels,
.font-size-labels,
.speed-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* Стили для переключателей */
.interface-settings {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    margin-top: 0.75rem;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: all var(--transition-hover);
    position: relative;
    overflow: hidden;
}

.setting-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
}

.setting-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    color: var(--primary-color);
    font-size: 0.9rem;
    transition: all var(--transition-hover);
    flex-shrink: 0;
}

.toggle-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-primary);
    transition: all var(--transition-hover);
    width: 100%;
    gap: 1rem;
}

.setting-title {
    font-weight: 500;
    color: var(--text-primary);
    flex: 1;
    text-align: left;
}

.setting-description {
    color: var(--text-muted);
    font-size: 0.8rem;
    line-height: 1.4;
    margin-left: 2.75rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.test-sound-btn {
    padding: 0.25rem 0.5rem;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.7rem;
    transition: all var(--transition-hover);
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-left: 0.5rem;
}

.test-sound-btn:hover {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
    transform: scale(1.05);
}

.toggle-label:hover .setting-title {
    color: var(--primary-color);
}

.toggle-label input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background: var(--bg-tertiary);
    border-radius: 12px;
    transition: all var(--transition-smooth);
    border: 2px solid var(--border-color);
    flex-shrink: 0;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    transition: all var(--transition-smooth);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-label input[type="checkbox"]:checked+.toggle-slider {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.toggle-label input[type="checkbox"]:checked+.toggle-slider::before {
    transform: translateX(20px);
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

.toggle-slider:hover {
    transform: scale(1.02);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
}

/* Стили для контейнеров слайдеров */
.font-size-container,
.speed-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.font-size-container input[type="range"],
.speed-container input[type="range"] {
    flex: 1;
}

.font-size-value,
.speed-value {
    font-weight: 600;
    color: var(--primary-color);
    min-width: 3rem;
    text-align: center;
    padding: 0.25rem 0.5rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

/* Улучшенные стили для range input */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: 3px;
    outline: none;
    transition: all var(--transition-hover);
}

input[type="range"]:hover {
    background: var(--bg-secondary);
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    transition: all var(--transition-hover);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
}

input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.4);
}

input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    transition: all var(--transition-hover);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
}

input[type="range"]::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.4);
}

/* Анимированные иконки для настроек */
.setting-item {
    position: relative;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    transition: all var(--transition-hover);
}

.setting-item:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

/* Анимированные эффекты для настроек */
.setting-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-color);
    border-radius: 0 3px 3px 0;
    transform: scaleY(0);
    transition: transform var(--transition-hover);
}

.setting-item:hover::before {
    transform: scaleY(1);
}

/* Специальные эффекты для разных типов настроек */
.setting-item:nth-child(1):hover::before {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
}

.setting-item:nth-child(2):hover::before {
    background: linear-gradient(45deg, var(--success-color), var(--primary-color));
}

.setting-item:nth-child(3):hover::before {
    background: linear-gradient(45deg, var(--warning-color), var(--primary-color));
}

.setting-item:nth-child(4):hover::before {
    background: linear-gradient(45deg, var(--accent-color), var(--success-color));
}

/* Активные состояния переключателей */
.setting-item:nth-child(1) input:checked+.toggle-slider {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border-color: var(--primary-color);
}

.setting-item:nth-child(2) input:checked+.toggle-slider {
    background: linear-gradient(45deg, var(--success-color), var(--primary-color));
    border-color: var(--success-color);
}

.setting-item:nth-child(3) input:checked+.toggle-slider {
    background: linear-gradient(45deg, var(--warning-color), var(--primary-color));
    border-color: var(--warning-color);
}

.setting-item:nth-child(4) input:checked+.toggle-slider {
    background: linear-gradient(45deg, var(--accent-color), var(--success-color));
    border-color: var(--accent-color);
}

/* Специальные стили для иконок настроек */
.setting-item:nth-child(1) .setting-icon {
    background: linear-gradient(45deg, rgba(37, 99, 235, 0.1), rgba(6, 182, 212, 0.1));
    color: var(--primary-color);
}

.setting-item:nth-child(2) .setting-icon {
    background: linear-gradient(45deg, rgba(16, 185, 129, 0.1), rgba(37, 99, 235, 0.1));
    color: var(--success-color);
}

.setting-item:nth-child(3) .setting-icon {
    background: linear-gradient(45deg, rgba(245, 158, 11, 0.1), rgba(37, 99, 235, 0.1));
    color: var(--warning-color);
}

.setting-item:nth-child(4) .setting-icon {
    background: linear-gradient(45deg, rgba(6, 182, 212, 0.1), rgba(16, 185, 129, 0.1));
    color: var(--accent-color);
}

.setting-item:hover .setting-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Анимация иконок при активации */
.setting-item:nth-child(1):hover .setting-icon {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    color: white;
}

.setting-item:nth-child(2):hover .setting-icon {
    background: linear-gradient(45deg, var(--success-color), var(--primary-color));
    color: white;
}

.setting-item:nth-child(3):hover .setting-icon {
    background: linear-gradient(45deg, var(--warning-color), var(--primary-color));
    color: white;
}

.setting-item:nth-child(4):hover .setting-icon {
    background: linear-gradient(45deg, var(--accent-color), var(--success-color));
    color: white;
}

/* Улучшенные стили для модального окна настроек */
.modal-body {
    max-height: 70vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--bg-tertiary);
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
    transition: background var(--transition-hover);
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover);
}

/* Анимация появления настроек */
.setting-group {
    animation: settingSlideIn 0.3s ease-out;
    animation-fill-mode: both;
}

.setting-group:nth-child(1) {
    animation-delay: 0.1s;
}

.setting-group:nth-child(2) {
    animation-delay: 0.2s;
}

.setting-group:nth-child(3) {
    animation-delay: 0.3s;
}

.setting-group:nth-child(4) {
    animation-delay: 0.4s;
}

.setting-group:nth-child(5) {
    animation-delay: 0.5s;
}

.setting-group:nth-child(6) {
    animation-delay: 0.6s;
}

.setting-group:nth-child(7) {
    animation-delay: 0.7s;
}

.setting-group:nth-child(8) {
    animation-delay: 0.8s;
}

@keyframes settingSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.api-help details {
    margin-top: 0.75rem;
}

.api-help summary {
    cursor: pointer;
    color: var(--primary-color);
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 0;
    transition: color var(--transition);
}

.api-help summary:hover {
    color: var(--primary-hover);
}

.help-content {
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    margin-top: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

.help-content ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.help-content li {
    margin-bottom: 0.25rem;
}

.help-content a {
    color: var(--primary-color);
    text-decoration: none;
}

.help-content a:hover {
    text-decoration: underline;
}

.confirm-content {
    text-align: center;
    padding: 1rem 0;
}

.confirm-icon {
    width: 64px;
    height: 64px;
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
}

.confirm-content p {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.confirm-content small {
    color: var(--text-muted);
}

.stats-display {
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-top: 0.5rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.stat-value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
}

/* Адаптивность */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        max-height: 200px;
        order: 2;
    }

    .chat-area {
        order: 1;
        flex: 1;
    }

    .suggestion-cards {
        grid-template-columns: 1fr;
    }

    .notification-container {
        left: 10px;
        right: 10px;
        max-width: none;
    }

    .notification {
        max-width: none;
    }

    .input-panel {
        padding: 1rem;
    }

    .app-header {
        padding: 0.75rem 1rem;
    }

    .header-content {
        gap: 1rem;
    }

    .app-title {
        font-size: 1.25rem;
    }

    .welcome-content h2 {
        font-size: 2rem;
    }

    .welcome-content p {
        font-size: 1rem;
    }
}

/* Подсветка найденного сообщения */
.message.highlighted {
    animation: messageHighlight 3s ease-in-out;
}

@keyframes messageHighlight {

    0%,
    100% {
        background: transparent;
        transform: scale(1);
    }

    10%,
    90% {
        background: rgba(37, 99, 235, 0.1);
        transform: scale(1.02);
    }

    50% {
        background: rgba(37, 99, 235, 0.15);
        transform: scale(1.02);
    }
}

/* Цветовая индикация скорости печати */
.typing-speed-indicator.speed-slow {
    color: var(--error-color);
}

.typing-speed-indicator.speed-slow i {
    color: var(--error-color);
    animation: slowTyping 2s ease-in-out infinite;
}

.typing-speed-indicator.speed-medium {
    color: var(--warning-color);
}

.typing-speed-indicator.speed-medium i {
    color: var(--warning-color);
    animation: mediumTyping 1.5s ease-in-out infinite;
}

.typing-speed-indicator.speed-fast {
    color: var(--success-color);
}

.typing-speed-indicator.speed-fast i {
    color: var(--success-color);
    animation: fastTyping 1s ease-in-out infinite;
}

.typing-speed-indicator.speed-very-fast {
    color: var(--accent-color);
    font-weight: 600;
}

.typing-speed-indicator.speed-very-fast i {
    color: var(--accent-color);
    animation: veryFastTyping 0.8s ease-in-out infinite;
}

.typing-speed-indicator.speed-very-fast #speedValue {
    font-weight: 700;
    text-shadow: 0 0 8px rgba(6, 182, 212, 0.3);
}

/* Анимации для разных скоростей печати */
@keyframes slowTyping {

    0%,
    100% {
        transform: scale(1);
        opacity: 0.7;
    }

    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

@keyframes mediumTyping {

    0%,
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.8;
    }

    50% {
        transform: scale(1.08) rotate(2deg);
        opacity: 1;
    }
}

@keyframes fastTyping {

    0%,
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.9;
    }

    25% {
        transform: scale(1.1) rotate(-1deg);
        opacity: 1;
    }

    75% {
        transform: scale(1.1) rotate(1deg);
        opacity: 1;
    }
}

@keyframes veryFastTyping {

    0%,
    100% {
        transform: scale(1) rotate(0deg);
    }

    25% {
        transform: scale(1.15) rotate(-2deg);
    }

    50% {
        transform: scale(1.2) rotate(0deg);
    }

    75% {
        transform: scale(1.15) rotate(2deg);
    }
}

/* Модальные окна */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-smooth);
    backdrop-filter: blur(0px);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.8) translateY(20px) rotateX(10deg);
    transition: all var(--transition-smooth);
    opacity: 0;
    position: relative;
}

.modal-overlay.active .modal-content {
    transform: scale(1) translateY(0) rotateX(0deg);
    opacity: 1;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.close-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition);
}

.close-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.btn-primary,
.btn-secondary,
.btn-danger {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: all var(--transition);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.setting-group input,
.setting-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-family: inherit;
    transition: all var(--transition);
}

.setting-group input:focus,
.setting-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.setting-group small {
    display: block;
    margin-top: 0.25rem;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.temperature-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.temperature-container input[type="range"] {
    flex: 1;
}

.temperature-value {
    min-width: 2rem;
    text-align: center;
    font-weight: 500;
    color: var(--primary-color);
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Индикатор загрузки - оптимизированная анимация */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
    color: white;
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto 1rem;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: smoothSpin 1s linear infinite;
    will-change: transform;
}

@keyframes smoothSpin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-spinner p {
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    opacity: 0.9;
}

/* Система уведомлений */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-width: 400px;
}

.notification {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(20px);
    pointer-events: auto;
    position: relative;
    overflow: hidden;
    transform: translateX(100%) scale(0.9);
    opacity: 0;
    transition: all var(--transition-smooth);
    max-width: 100%;
    word-wrap: break-word;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    transition: all var(--transition-smooth);
}

.notification-show {
    transform: translateX(0) scale(1);
    opacity: 1;
    animation: notificationSlideIn 0.5s var(--transition-smooth);
}

.notification-hide {
    transform: translateX(100%) scale(0.9);
    opacity: 0;
    animation: notificationSlideOut 0.3s var(--transition-smooth);
}

@keyframes notificationSlideIn {
    0% {
        transform: translateX(100%) scale(0.9) rotateY(15deg);
        opacity: 0;
    }

    60% {
        transform: translateX(-5px) scale(1.02) rotateY(-2deg);
        opacity: 0.9;
    }

    100% {
        transform: translateX(0) scale(1) rotateY(0deg);
        opacity: 1;
    }
}

@keyframes notificationSlideOut {
    0% {
        transform: translateX(0) scale(1);
        opacity: 1;
    }

    100% {
        transform: translateX(100%) scale(0.9);
        opacity: 0;
    }
}

.notification-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 0.875rem;
    transition: all var(--transition-smooth);
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.notification-message {
    font-size: 0.875rem;
    line-height: 1.4;
    color: var(--text-secondary);
}

.notification-close {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all var(--transition-smooth);
    opacity: 0.7;
}

.notification-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    opacity: 1;
    transform: scale(1.1);
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--primary-color);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    animation: notificationProgress linear;
    transform-origin: left;
}

@keyframes notificationProgress {
    from {
        transform: scaleX(1);
    }

    to {
        transform: scaleX(0);
    }
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
    flex-wrap: wrap;
}

.notification-action {
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: all var(--transition-smooth);
}

.notification-action:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.notification-action.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.notification-action.primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px) scale(1.02);
}

/* Типы уведомлений */
.notification-success {
    border-color: var(--success-color);
}

.notification-success::before {
    background: var(--success-color);
}

.notification-success .notification-icon {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.notification-success .notification-progress {
    background: var(--success-color);
}

.notification-error {
    border-color: var(--error-color);
}

.notification-error::before {
    background: var(--error-color);
}

.notification-error .notification-icon {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.notification-error .notification-progress {
    background: var(--error-color);
}

.notification-warning {
    border-color: var(--warning-color);
}

.notification-warning::before {
    background: var(--warning-color);
}

.notification-warning .notification-icon {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.notification-warning .notification-progress {
    background: var(--warning-color);
}

.notification-info {
    border-color: var(--accent-color);
}

.notification-info::before {
    background: var(--accent-color);
}

.notification-info .notification-icon {
    background: rgba(6, 182, 212, 0.1);
    color: var(--accent-color);
}

.notification-info .notification-progress {
    background: var(--accent-color);
}

/* Hover эффекты */
.notification:hover {
    transform: translateX(-2px) scale(1.01);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.notification-hovered .notification-progress {
    animation-play-state: paused;
}

.notification:hover .notification-icon {
    transform: scale(1.1) rotate(5deg);
}

.notification:hover .notification-close {
    opacity: 1;
}

/* Анимация иконок */
.notification-success .notification-icon i {
    animation: successPulse 2s ease-in-out infinite;
}

.notification-error .notification-icon i {
    animation: errorShake 0.5s ease-in-out;
}

.notification-warning .notification-icon i {
    animation: warningBlink 1s ease-in-out infinite;
}

.notification-info .notification-icon i {
    animation: infoBounce 2s ease-in-out infinite;
}

@keyframes successPulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }
}

@keyframes errorShake {

    0%,
    100% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-2px);
    }

    75% {
        transform: translateX(2px);
    }
}

@keyframes warningBlink {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

@keyframes infoBounce {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-2px);
    }
}

/* Адаптивность */
@media (max-width: 768px) {
    .sidebar {
        width: 280px;
    }

    .app-header {
        padding: 1rem;
    }

    .chat-container {
        padding: 1rem;
    }

    .input-panel {
        padding: 1rem;
    }

    .welcome-content h2 {
        font-size: 2rem;
    }

    .suggestion-cards {
        grid-template-columns: 1fr;
    }

    .notification-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .notification {
        max-width: 100%;
    }
}

@media (max-width: 640px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        max-height: 200px;
    }

    .chat-management-buttons {
        flex-wrap: wrap;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .notification-actions {
        flex-direction: column;
    }

    .notification-action {
        justify-content: center;
    }
}

/* Ст
или для прикрепленных файлов */
.attached-files-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    margin: 0 2rem 1rem;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-smooth);
    animation: slideInUp 0.3s var(--transition-smooth);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.attached-files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.attached-files-header span {
    font-weight: 500;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.attached-files-header i {
    color: var(--primary-color);
}

.clear-all-files-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-muted);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all var(--transition-smooth);
}

.clear-all-files-btn:hover {
    background: var(--error-color);
    color: white;
    transform: scale(1.1);
}

.attached-files-list {
    padding: 0.5rem;
    max-height: 200px;
    overflow-y: auto;
}

.attached-file-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.attached-file-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.05), transparent);
    transition: left var(--transition-slow);
}

.attached-file-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
    transform: translateX(2px);
}

.attached-file-item:hover::before {
    left: 100%;
}

.attached-file-item:last-child {
    margin-bottom: 0;
}

.file-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    color: var(--primary-color);
    font-size: 1rem;
    flex-shrink: 0;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0.25rem;
}

.file-size {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.file-actions {
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity var(--transition);
}

.attached-file-item:hover .file-actions {
    opacity: 1;
}

.file-action-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all var(--transition-smooth);
}

.file-action-btn:hover {
    transform: scale(1.1);
}

.file-action-btn.preview-btn:hover {
    background: var(--accent-color);
    color: white;
}

.file-action-btn.remove-btn:hover {
    background: var(--error-color);
    color: white;
}

/* Модальное окно предварительного просмотра файлов */
.file-preview-modal .modal-content {
    max-width: 800px;
    max-height: 90vh;
}

.file-preview-content {
    display: flex;
    flex-direction: column;
}

.file-preview-body {
    flex: 1;
    overflow: auto;
    max-height: 60vh;
}

.image-preview {
    text-align: center;
    padding: 1rem;
}

.image-preview img {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
}

.text-preview {
    padding: 1rem;
}

.text-preview pre {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--border-radius);
    overflow: auto;
    max-height: 400px;
    font-size: 0.875rem;
    line-height: 1.4;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.text-preview code {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.pdf-preview {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
}

.pdf-preview i {
    font-size: 3rem;
    color: var(--error-color);
    margin-bottom: 1rem;
}

.file-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: var(--text-muted);
    flex-wrap: wrap;
}

.file-meta span {
    padding: 0.25rem 0.5rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
}

/* Улучшения для кнопки прикрепления файлов */
.attach-btn {
    position: relative;
}

.attach-btn::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    opacity: 0;
    transform: scale(0);
    transition: all var(--transition-smooth);
}

.attach-btn.has-files::after {
    opacity: 1;
    transform: scale(1);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
    .attached-files-container {
        margin: 0 1rem 1rem;
    }

    .file-preview-modal .modal-content {
        max-width: 95%;
        margin: 1rem;
    }

    .file-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .attached-file-item {
        flex-wrap: wrap;
    }

    .file-actions {
        opacity: 1;
        margin-top: 0.5rem;
        width: 100%;
        justify-content: flex-end;
    }
}

/* Drag and Drop стили */
.input-wrapper.drag-over {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    transform: translateY(-2px);
}

.input-wrapper.drag-over::before {
    opacity: 1;
}

.drag-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(37, 99, 235, 0.1);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-smooth);
}

.drag-overlay.active {
    opacity: 1;
    visibility: visible;
}

.drag-content {
    text-align: center;
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 600;
}

.drag-content i {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: bounce 1s infinite;
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-10px);
    }

    60% {
        transform: translateY(-5px);
    }
}

/* ===
== СТИЛИ ДЛЯ РАБОТЫ С ФАЙЛАМИ ===== */

/* Контейнер прикрепленных файлов */
.attached-files-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    margin: 1rem 2rem 0;
    overflow: hidden;
    animation: slideDown 0.3s var(--transition-smooth);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }

    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 300px;
    }
}

.attached-files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.attached-files-header i {
    color: var(--primary-color);
    margin-right: 0.5rem;
}

.clear-files-btn {
    background: transparent;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    transition: all var(--transition);
}

.clear-files-btn:hover {
    background: var(--bg-primary);
    color: var(--error-color);
}

.attached-files-list {
    padding: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    max-height: 200px;
    overflow-y: auto;
}

.attached-file-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    min-width: 250px;
    max-width: 300px;
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.attached-file-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--primary-color);
    transition: all var(--transition-smooth);
}

.attached-file-item.image::before {
    background: var(--success-color);
}

.attached-file-item.text::before {
    background: var(--accent-color);
}

.attached-file-item.document::before {
    background: var(--warning-color);
}

.attached-file-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.file-preview {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
}

.file-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.file-icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.attached-file-item.image .file-icon {
    color: var(--success-color);
}

.attached-file-item.text .file-icon {
    color: var(--accent-color);
}

.attached-file-item.document .file-icon {
    color: var(--warning-color);
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0.25rem;
}

.file-details {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.file-preview-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    line-height: 1.3;
    max-height: 2.6em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-clamp: 2;
}

.remove-file-btn {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all var(--transition);
    opacity: 0.7;
}

.remove-file-btn:hover {
    background: var(--error-color);
    color: white;
    opacity: 1;
    transform: scale(1.1);
}

/* Файлы в сообщениях */
.message-files {
    margin-top: 0.75rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.message-file {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    max-width: 200px;
    transition: all var(--transition);
}

.message.user .message-file {
    background: rgba(255, 255, 255, 0.2);
}

.message.ai .message-file {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
}

.message-file:hover {
    transform: scale(1.02);
}

.message-file.image {
    flex-direction: column;
    text-align: center;
    padding: 0.75rem;
}

.message-file.image img {
    width: 100%;
    max-width: 150px;
    height: auto;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition);
}

.message-file.image img:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.message-file .file-icon {
    width: 32px;
    height: 32px;
    font-size: 1rem;
}

.message-file .file-info {
    flex: 1;
    min-width: 0;
}

.message-file .file-name {
    font-size: 0.75rem;
    margin-bottom: 0.125rem;
}

.message-file .file-size {
    font-size: 0.625rem;
    color: var(--text-muted);
}

.message.user .message-file .file-name,
.message.user .message-file .file-size {
    color: rgba(255, 255, 255, 0.9);
}

/* Модальное окно для просмотра изображений */
.image-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    cursor: pointer;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.image-modal-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }

    to {
        transform: scale(1);
        opacity: 1;
    }
}

.image-modal-content img {
    max-width: 100%;
    max-height: 100%;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

.image-modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 1.5rem;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition);
    backdrop-filter: blur(10px);
}

.image-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Улучшения для кнопки прикрепления */
.attach-btn {
    position: relative;
}

.attach-btn.has-files {
    background: var(--success-color);
    color: white;
}

.attach-btn.has-files::after {
    content: attr(data-count);
    position: absolute;
    top: -6px;
    right: -6px;
    background: var(--error-color);
    color: white;
    font-size: 0.625rem;
    font-weight: 600;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--bg-secondary);
}

/* Drag & Drop стили */
.input-wrapper.drag-over {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    transform: translateY(-2px);
}

.input-wrapper.drag-over::before {
    opacity: 1;
}

/* Анимация для добавления файлов */
.attached-file-item {
    animation: fileSlideIn 0.3s var(--transition-smooth);
}

@keyframes fileSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px) scale(0.9);
    }

    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

/* Адаптивность */
@media (max-width: 768px) {
    .attached-files-container {
        margin: 1rem;
    }

    .attached-file-item {
        min-width: 200px;
        max-width: 100%;
    }

    .attached-files-list {
        flex-direction: column;
    }

    .message-files {
        flex-direction: column;
    }

    .message-file {
        max-width: 100%;
    }
}

/* Темная тема для файлов */
[data-theme="dark"] .attached-file-item {
    background: var(--bg-tertiary);
}

[data-theme="dark"] .message-file {
    background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .message.ai .message-file {
    background: var(--bg-primary);
}

[data-theme="dark"] .image-modal-overlay {
    background: rgba(0, 0, 0, 0.95);
}

/* 
Дополнительные стили для загрузки изображений */

/* Анимация для drag-over состояния */
.input-wrapper.drag-over {
    border-color: var(--primary-color) !important;
    background: rgba(37, 99, 235, 0.05) !important;
    transform: translateY(-2px) scale(1.01) !important;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.2) !important;
}

.input-wrapper.drag-over::before {
    opacity: 1 !important;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(6, 182, 212, 0.1) 100%) !important;
}

/* Стили для превью изображений в прикрепленных файлах */
.file-preview-image {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    transition: all var(--transition-smooth);
}

.file-preview-image:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-md);
}

.file-preview-image img {
    transition: all var(--transition-smooth);
    cursor: pointer;
}

.file-preview-image img:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

/* Стили для изображений в сообщениях */
.message-file-image {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    transition: all var(--transition-smooth);
}

.message-file-image:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-sm);
}

.message-file-image img {
    transition: all var(--transition-smooth);
    cursor: pointer;
}

.message-file-image img:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

/* Модальное окно для просмотра изображений */
.image-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-smooth);
    backdrop-filter: blur(0px);
}

.image-modal-overlay.active {
    opacity: 1;
    visibility: visible;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(8px);
}

.image-modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.8) translateY(20px);
    transition: all var(--transition-smooth);
    opacity: 0;
    border: 1px solid var(--border-color);
}

.image-modal-overlay.active .image-modal-content {
    transform: scale(1) translateY(0);
    opacity: 1;
}

.image-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.image-modal-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 400px;
}

.image-modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-smooth);
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.image-modal-close::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(239, 68, 68, 0.2) 0%, transparent 70%);
    transition: all var(--transition-smooth);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.image-modal-close:hover::before {
    width: 50px;
    height: 50px;
}

.image-modal-close:hover {
    background: var(--error-color);
    color: white;
    transform: scale(1.1);
}

.image-modal-body {
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: calc(90vh - 80px);
    background: var(--bg-primary);
}

.image-modal-body img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-smooth);
}

/* Улучшенные стили для кнопки прикрепления */
.attach-btn {
    position: relative;
    overflow: hidden;
}

.attach-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(37, 99, 235, 0.2) 0%, transparent 70%);
    transition: all var(--transition-smooth);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.attach-btn:hover::after {
    width: 60px;
    height: 60px;
}

/* Анимация для появления прикрепленных файлов */
.attached-files-container {
    animation: slideInUp 0.3s var(--transition-smooth);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Анимация для файлов в списке */
.file-preview {
    animation: fadeInScale 0.3s var(--transition-smooth);
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Улучшенные стили для кнопки удаления файла */
.remove-file-btn {
    position: relative;
    overflow: hidden;
}

.remove-file-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(239, 68, 68, 0.2) 0%, transparent 70%);
    transition: all var(--transition-smooth);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.remove-file-btn:hover::before {
    width: 40px;
    height: 40px;
}

/* Стили для индикатора загрузки файлов */
.file-loading {
    position: relative;
}

.file-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(37, 99, 235, 0.1);
    border-radius: var(--border-radius);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 0.3;
    }

    50% {
        opacity: 0.7;
    }
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
    .image-modal-content {
        max-width: 95vw;
        max-height: 95vh;
        margin: 1rem;
    }

    .image-modal-header {
        padding: 0.75rem 1rem;
    }

    .image-modal-header h3 {
        font-size: 1rem;
        max-width: 250px;
    }

    .image-modal-body {
        padding: 0.75rem;
        max-height: calc(95vh - 70px);
    }

    .file-preview {
        padding: 0.5rem;
    }

    .file-preview-image,
    .file-icon {
        width: 40px;
        height: 40px;
    }

    .message-file-image,
    .message-file-icon {
        width: 32px;
        height: 32px;
    }
}

/* Темная тема для модального окна */
[data-theme="dark"] .image-modal-overlay.active {
    background: rgba(0, 0, 0, 0.9);
}

[data-theme="dark"] .image-modal-body {
    background: var(--bg-primary);
}

/* Стили для пустого состояния */
.no-files-message {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 2rem;
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-lg);
    margin: 1rem 0;
    transition: all var(--transition-smooth);
}

.no-files-message:hover {
    border-color: var(--primary-color);
    color: var(--text-secondary);
}

.no-files-message i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--text-muted);
}

/* Допо
лнительные анимации для переключения темы */

/* Плавные переходы для всех интерактивных элементов */
button,
input,
textarea,
select,
.message,
.chat-history-item,
.suggestion-card {
    transition:
        background-color var(--theme-transition-duration) var(--theme-transition-easing),
        border-color var(--theme-transition-duration) var(--theme-transition-easing),
        color var(--theme-transition-duration) var(--theme-transition-easing),
        box-shadow var(--theme-transition-duration) var(--theme-transition-easing) !important;
}

/* Анимация мерцания при переключении темы */
@keyframes themeShimmer {
    0% {
        background-position: -200% 0;
    }

    100% {
        background-position: 200% 0;
    }
}

.theme-shimmer-effect {
    position: relative;
    overflow: hidden;
}

.theme-shimmer-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.1),
            transparent);
    background-size: 200% 100%;
    animation: themeShimmer 1.5s ease-in-out;
    pointer-events: none;
    z-index: 1;
}

[data-theme="dark"] .theme-shimmer-effect::before {
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.05),
            transparent);
}

/* Эффект пульсации для кнопки темы */
.theme-toggle.switching {
    animation: themePulse 0.8s ease-in-out;
}

@keyframes themePulse {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.4);
    }

    25% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(37, 99, 235, 0.2);
    }

    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 20px rgba(37, 99, 235, 0.1);
    }

    75% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(37, 99, 235, 0.05);
    }
}

[data-theme="dark"] .theme-toggle.switching {
    animation: themePulseDark 0.8s ease-in-out;
}

@keyframes themePulseDark {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4);
    }

    25% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(245, 158, 11, 0.2);
    }

    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 20px rgba(245, 158, 11, 0.1);
    }

    75% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(245, 158, 11, 0.05);
    }
}

/* Анимация для иконки в кнопке темы */
.theme-toggle i {
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Дополнительные эффекты для темной темы */
[data-theme="dark"] {
    --theme-glow: 0 0 20px rgba(37, 99, 235, 0.1);
}

[data-theme="light"] {
    --theme-glow: 0 0 20px rgba(0, 0, 0, 0.05);
}

/* Плавное появление элементов после переключения темы */
.theme-transition-element {
    transition: all var(--theme-transition-duration) var(--theme-transition-easing);
}

/* Эффект свечения для активных элементов в темной теме */
[data-theme="dark"] .message.ai .message-content:hover,
[data-theme="dark"] .chat-history-item:hover,
[data-theme="dark"] .suggestion-card:hover {
    box-shadow: var(--theme-glow), var(--shadow-md);
}

/* Анимация градиента для кнопки переключения темы */
.theme-toggle {
    background-size: 200% 200%;
    transition: all var(--transition-smooth), background-position 0.8s ease;
}

.theme-toggle:hover {
    background-position: 100% 100%;
}



/* Улучшенная анимация волны */
.theme-wave-effect {
    background: radial-gradient(circle, var(--bg-primary) 0%, transparent 70%);
    filter: blur(1px);
}

.theme-wave-effect.active {
    animation: themeWaveExpandImproved 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes themeWaveExpandImproved {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
        filter: blur(0px);
    }

    30% {
        opacity: 0.9;
        filter: blur(2px);
    }

    70% {
        opacity: 0.5;
        filter: blur(4px);
    }

    100% {
        width: 300vw;
        height: 300vh;
        opacity: 0;
        filter: blur(8px);
    }
}